#ifndef FEATURE_OPERATORS_HPP
#define FEATURE_OPERATORS_HPP

// Include the type definitions (e.g., DataFrame)
#include "feature_operators/types.hpp"

// Include all individual operator modules
#include "feature_operators/core_math.hpp"
#include "feature_operators/data_utils.hpp"
#include "feature_operators/logical_ops.hpp"
#include "feature_operators/comparison_ops.hpp"
#include "feature_operators/reduction_ops.hpp"
#include "feature_operators/panel_ops.hpp"
#include "feature_operators/timeseries_ops.hpp"
#include "feature_operators/group_ops.hpp"

// Any other general utilities or forward declarations for the library could go here.

#endif // FEATURE_OPERATORS_HPP
