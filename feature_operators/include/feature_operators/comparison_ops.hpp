#ifndef FEATURE_OPERATORS_COMPARISON_OPS_HPP
#define FEATURE_OPERATORS_COMPARISON_OPS_HPP

#include "feature_operators/types.hpp" // Added
#include <Eigen/Dense> // Kept for other Eigen types

namespace feature_operators {

// Array vs Array
DataFrame Mthan(const DataFrame& a, const DataFrame& b);
DataFrame MEthan(const DataFrame& a, const DataFrame& b);
DataFrame Lthan(const DataFrame& a, const DataFrame& b);
DataFrame LEthan(const DataFrame& a, const DataFrame& b);
DataFrame Equal(const DataFrame& a, const DataFrame& b);
DataFrame UnEqual(const DataFrame& a, const DataFrame& b);

// Array vs Scalar
DataFrame Mthan(const DataFrame& a, double b_scalar);
DataFrame MEthan(const DataFrame& a, double b_scalar);
DataFrame Lthan(const DataFrame& a, double b_scalar);
DataFrame LEthan(const DataFrame& a, double b_scalar);
DataFrame Equal(const DataFrame& a, double b_scalar);
DataFrame UnEqual(const DataFrame& a, double b_scalar);

} // namespace feature_operators
#endif // FEATURE_OPERATORS_COMPARISON_OPS_HPP
