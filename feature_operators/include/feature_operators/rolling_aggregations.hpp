#ifndef FEATURE_OPERATORS_ROLLING_AGGREGATIONS_HPP
#define FEATURE_OPERATORS_ROLLING_AGGREGATIONS_HPP

#include <cmath>
#include <cstdint>
#include <limits>
#include <tuple>
#include <vector>
#include <Eigen/Dense>

namespace feature_operators {

// Forward declaration
using DataFrame = Eigen::ArrayXXd;

namespace rolling {

const double NaN = std::numeric_limits<double>::quiet_NaN();

// ----------------------------------------------------------------------
// Monotonic bounds checking (from pandas algos.pyx)

/**
 * Check if array is monotonic (based on pandas algos.pyx)
 * @param arr Array to check
 * @param timelike Whether this is a time-like array (unused in our implementation)
 * @return Tuple of (is_monotonic_inc, is_monotonic_dec, is_strict_monotonic)
 */
std::tuple<bool, bool, bool> is_monotonic(const std::vector<int64_t>& arr, bool timelike = false);

/**
 * Check if start/end bounds are monotonic increasing
 * @param start Start indices array
 * @param end End indices array
 * @return True if both arrays are monotonic increasing
 */
bool is_monotonic_increasing_start_end_bounds(const std::vector<int64_t>& start,
                                               const std::vector<int64_t>& end);

// ----------------------------------------------------------------------
// Rolling mean

/**
 * Calculate mean based on pandas implementation
 */
double calc_mean(int64_t minp, int64_t nobs, int64_t neg_ct,
                 double sum_x, int64_t num_consecutive_same_value,
                 double prev_value) noexcept;

/**
 * Add a value to mean calculation using Kahan summation
 */
void add_mean(double val, int64_t &nobs, double &sum_x, int64_t &neg_ct,
              double &compensation, int64_t &num_consecutive_same_value,
              double &prev_value) noexcept;

/**
 * Remove a value from mean calculation using Kahan summation
 */
void remove_mean(double val, int64_t &nobs, double &sum_x, int64_t &neg_ct,
                 double &compensation) noexcept;

// ----------------------------------------------------------------------
// Rolling variance

/**
 * Calculate variance based on pandas implementation
 */
double calc_var(int64_t minp, int ddof, double nobs, double ssqdm_x,
                int64_t num_consecutive_same_value) noexcept;

/**
 * Add a value to variance calculation using Welford's method
 */
void add_var(double val, double &nobs, double &mean_x, double &ssqdm_x,
             double &compensation, int64_t &num_consecutive_same_value,
             double &prev_value) noexcept;

/**
 * Remove a value from variance calculation using Welford's method
 */
void remove_var(double val, double &nobs, double &mean_x, double &ssqdm_x,
                double &compensation) noexcept;

// ----------------------------------------------------------------------
// Rolling skewness

/**
 * Calculate skewness based on pandas implementation
 */
double calc_skew(int64_t minp, int64_t nobs, double x, double xx, double xxx,
                 int64_t num_consecutive_same_value) noexcept;

/**
 * Add a value to skewness calculation using Kahan summation
 */
void add_skew(double val, int64_t &nobs, double &x, double &xx, double &xxx,
              double &compensation_x, double &compensation_xx,
              double &compensation_xxx, int64_t &num_consecutive_same_value,
              double &prev_value) noexcept;

/**
 * Remove a value from skewness calculation using Kahan summation
 */
void remove_skew(double val, int64_t &nobs, double &x, double &xx, double &xxx,
                 double &compensation_x, double &compensation_xx,
                 double &compensation_xxx) noexcept;

// ----------------------------------------------------------------------
// Rolling kurtosis

/**
 * Calculate kurtosis based on pandas implementation
 */
double calc_kurt(int64_t minp, int64_t nobs, double x, double xx, double xxx, double xxxx,
                 int64_t num_consecutive_same_value) noexcept;

/**
 * Add a value to kurtosis calculation using Kahan summation
 */
void add_kurt(double val, int64_t &nobs, double &x, double &xx, double &xxx, double &xxxx,
              double &compensation_x, double &compensation_xx,
              double &compensation_xxx, double &compensation_xxxx,
              int64_t &num_consecutive_same_value, double &prev_value) noexcept;

/**
 * Remove a value from kurtosis calculation using Kahan summation
 */
void remove_kurt(double val, int64_t &nobs, double &x, double &xx, double &xxx, double &xxxx,
                 double &compensation_x, double &compensation_xx,
                 double &compensation_xxx, double &compensation_xxxx) noexcept;

// ----------------------------------------------------------------------
// Rolling sum

/**
 * Calculate sum based on pandas implementation
 */
double calc_sum(int64_t minp, int64_t nobs, double sum_x) noexcept;

/**
 * Add a value to sum calculation using Kahan summation
 */
void add_sum(double val, int64_t &nobs, double &sum_x, double &compensation) noexcept;

/**
 * Remove a value from sum calculation using Kahan summation
 */
void remove_sum(double val, int64_t &nobs, double &sum_x, double &compensation) noexcept;

// ----------------------------------------------------------------------
// Rolling min/max

/**
 * Calculate min based on pandas implementation
 */
double calc_min(int64_t minp, int64_t nobs, double min_val) noexcept;

/**
 * Calculate max based on pandas implementation
 */
double calc_max(int64_t minp, int64_t nobs, double max_val) noexcept;

/**
 * Add a value to min calculation
 */
void add_min(double val, int64_t &nobs, double &min_val) noexcept;

/**
 * Add a value to max calculation
 */
void add_max(double val, int64_t &nobs, double &max_val) noexcept;

/**
 * Remove a value from min calculation (requires recalculation)
 */
void remove_min(double val, int64_t &nobs, double &min_val) noexcept;

/**
 * Remove a value from max calculation (requires recalculation)
 */
void remove_max(double val, int64_t &nobs, double &max_val) noexcept;

// ----------------------------------------------------------------------
// High-level rolling window functions (similar to pandas rolling API)

/**
 * Rolling mean calculation
 * @param data Input DataFrame
 * @param window Window size
 * @param min_periods Minimum number of observations required to have a value
 * @return DataFrame with rolling mean values
 */
DataFrame roll_mean(const DataFrame &data, int window, int min_periods = 1);

/**
 * Rolling standard deviation calculation
 * @param data Input DataFrame
 * @param window Window size
 * @param min_periods Minimum number of observations required to have a value
 * @param ddof Delta degrees of freedom (default 1 for sample std)
 * @return DataFrame with rolling standard deviation values
 */
DataFrame roll_std(const DataFrame &data, int window, int min_periods = 1, int ddof = 1);

/**
 * Rolling variance calculation
 * @param data Input DataFrame
 * @param window Window size
 * @param min_periods Minimum number of observations required to have a value
 * @param ddof Delta degrees of freedom (default 1 for sample variance)
 * @return DataFrame with rolling variance values
 */
DataFrame roll_var(const DataFrame &data, int window, int min_periods = 1, int ddof = 1);

/**
 * Rolling sum calculation
 * @param data Input DataFrame
 * @param window Window size
 * @param min_periods Minimum number of observations required to have a value
 * @return DataFrame with rolling sum values
 */
DataFrame roll_sum(const DataFrame &data, int window, int min_periods = 1);

/**
 * Rolling minimum calculation
 * @param data Input DataFrame
 * @param window Window size
 * @param min_periods Minimum number of observations required to have a value
 * @return DataFrame with rolling minimum values
 */
DataFrame roll_min(const DataFrame &data, int window, int min_periods = 1);

/**
 * Rolling maximum calculation
 * @param data Input DataFrame
 * @param window Window size
 * @param min_periods Minimum number of observations required to have a value
 * @return DataFrame with rolling maximum values
 */
DataFrame roll_max(const DataFrame &data, int window, int min_periods = 1);

/**
 * Rolling median calculation
 * @param data Input DataFrame
 * @param window Window size
 * @param min_periods Minimum number of observations required to have a value
 * @return DataFrame with rolling median values
 */
DataFrame roll_median(const DataFrame &data, int window, int min_periods = 1);

/**
 * Rolling skewness calculation
 * @param data Input DataFrame
 * @param window Window size
 * @param min_periods Minimum number of observations required to have a value
 * @return DataFrame with rolling skewness values
 */
DataFrame roll_skew(const DataFrame &data, int window, int min_periods = 1);

/**
 * Rolling kurtosis calculation
 * @param data Input DataFrame
 * @param window Window size
 * @param min_periods Minimum number of observations required to have a value
 * @return DataFrame with rolling kurtosis values
 */
DataFrame roll_kurt(const DataFrame &data, int window, int min_periods = 1);

/**
 * Rolling quantile calculation
 * @param data Input DataFrame
 * @param window Window size
 * @param quantile Quantile to compute (0.0 to 1.0)
 * @param min_periods Minimum number of observations required to have a value
 * @return DataFrame with rolling quantile values
 */
DataFrame roll_quantile(const DataFrame &data, int window, double quantile, int min_periods = 1);

} // namespace rolling
} // namespace feature_operators

#endif // FEATURE_OPERATORS_ROLLING_AGGREGATIONS_HPP
