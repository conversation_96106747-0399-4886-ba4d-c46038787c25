#ifndef FEATURE_OPERATORS_GROUP_OPS_HPP
#define FEATURE_OPERATORS_GROUP_OPS_HPP

#include "feature_operators/types.hpp" // Added
#include <Eigen/Dense> // Kept for other Eigen types

namespace feature_operators {

DataFrame pn_GroupNeutral(const DataFrame& data, const DataFrame& labels);
DataFrame pn_GroupRank(const DataFrame& data, const DataFrame& labels);
DataFrame pn_GroupNorm(const DataFrame& data, const DataFrame& labels);

} // namespace feature_operators
#endif // FEATURE_OPERATORS_GROUP_OPS_HPP
