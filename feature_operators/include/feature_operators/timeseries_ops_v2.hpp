#ifndef FEATURE_OPERATORS_TIMESERIES_OPS_V2_HPP
#define FEATURE_OPERATORS_TIMESERIES_OPS_V2_HPP

#include "feature_operators/types.hpp"

namespace feature_operators {
namespace v2 {

/**
 * Refactored ts series functions using rolling window aggregations
 * These functions follow the same pattern as Python implementation
 */

// Basic rolling window functions
DataFrame ts_Delay_v2(const DataFrame& data, int n);
DataFrame ts_Mean_v2(const DataFrame& data, int n);
DataFrame ts_Stdev_v2(const DataFrame& data, int n);
DataFrame ts_Delta_v2(const DataFrame& data, int n);
DataFrame ts_Min_v2(const DataFrame& data, int n);
DataFrame ts_Max_v2(const DataFrame& data, int n);
DataFrame ts_Skewness_v2(const DataFrame& data, int n);
DataFrame ts_Kurtosis_v2(const DataFrame& data, int n);
DataFrame ts_Scale_v2(const DataFrame& data, int n);
DataFrame ts_Corr_v2(const DataFrame& data1, const DataFrame& data2, int n);
DataFrame ts_Cov_v2(const DataFrame& data1, const DataFrame& data2, int n);
DataFrame ts_Regression_v2(const DataFrame& data1, const DataFrame& data2, int n, char rettype);
DataFrame ts_Entropy_v2(const DataFrame& data, int n, int bucket = 10);
DataFrame ts_Partial_corr_v2(const DataFrame& data1, const DataFrame& data2, const DataFrame& data3, int n);
DataFrame ts_TransNorm_v2(const DataFrame& data, int n);
DataFrame ts_Decay_v2(const DataFrame& data, int n);
DataFrame ts_Decay2_v2(const DataFrame& data, int n);
DataFrame ts_Rank_v2(const DataFrame& data, int n);
DataFrame ts_Median_v2(const DataFrame& data, int n);
DataFrame ts_Argmax_v2(const DataFrame& data, int n);
DataFrame ts_Argmin_v2(const DataFrame& data, int n);
DataFrame ts_Sum_v2(const DataFrame& data, int n);
DataFrame ts_Product_v2(const DataFrame& data, int n);
DataFrame ts_Divide_v2(const DataFrame& data, int n);
DataFrame ts_ChgRate_v2(const DataFrame& data, int n);
DataFrame ts_MaxDD_v2(const DataFrame& data, int n);
DataFrame ts_Quantile_v2(const DataFrame& data, int n, char rettype);
DataFrame ts_MeanChg_v2(const DataFrame& data, int n);

// Tot_ series wrapper functions (fixed window of 15)
DataFrame Tot_Mean_v2(const DataFrame& data);
DataFrame Tot_Sum_v2(const DataFrame& data);
DataFrame Tot_Stdev_v2(const DataFrame& data);
DataFrame Tot_Delta_v2(const DataFrame& data);
DataFrame Tot_Divide_v2(const DataFrame& data);
DataFrame Tot_ChgRate_v2(const DataFrame& data);
DataFrame Tot_Rank_v2(const DataFrame& data);
DataFrame Tot_Min_v2(const DataFrame& data);
DataFrame Tot_Max_v2(const DataFrame& data);
DataFrame Tot_ArgMax_v2(const DataFrame& data);
DataFrame Tot_ArgMin_v2(const DataFrame& data);

} // namespace v2
} // namespace feature_operators

#endif // FEATURE_OPERATORS_TIMESERIES_OPS_V2_HPP
