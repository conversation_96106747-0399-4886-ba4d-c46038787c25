// #ifndef FEATURE_OPERATORS_REDUCTION_OPS_HPP
// #define FEATURE_OPERATORS_REDUCTION_OPS_HPP

// #include "feature_operators/types.hpp" // Added
// #include <Eigen/Dense> // Kept for other Eigen types

// namespace feature_operators {

// DataFrame Min(const DataFrame& a, const DataFrame& b);
// DataFrame Min(const DataFrame& a, double b_scalar);
// DataFrame Min(double a_scalar, const DataFrame& b);

// DataFrame Max(const DataFrame& a, const DataFrame& b);
// DataFrame Max(const DataFrame& a, double b_scalar);
// DataFrame Max(double a_scalar, const DataFrame& b);

// } // namespace feature_operators
// #endif // FEATURE_OPERATORS_REDUCTION_OPS_HPP

#include "feature_operators/types.hpp"         // Defines DataFrame, or this file now provides generic Eigen ops
#include <Eigen/Dense>                         // For Eigen::ArrayBase and core Eigen functionalities
#include <cmath>                               // For std::isnan, std::fmin, std::fmax (or std::min/max)
#include <limits>                              // For std::numeric_limits
#include <algorithm>                           // For std::min and std::max
#include <cassert>                             // For assert
#include <type_traits>                         // For std::is_floating_point

namespace feature_operators {

// Custom fmin-like behavior: if one is NaN, return the other; if both NaN, return NaN.
// Templated to work with any scalar type that supports std::isnan and std::min.
template <typename Scalar>
inline Scalar fmin_custom(Scalar val_a, Scalar val_b) {
    static_assert(std::is_floating_point<Scalar>::value,
                  "fmin_custom is intended for floating-point scalar types due to NaN handling.");
    bool a_is_nan = std::isnan(val_a);
    bool b_is_nan = std::isnan(val_b);
    if (a_is_nan && b_is_nan) return std::numeric_limits<Scalar>::quiet_NaN();
    if (a_is_nan) return val_b;
    if (b_is_nan) return val_a;
    return std::min(val_a, val_b);
}

// Custom fmax-like behavior: if one is NaN, return the other; if both NaN, return NaN.
// Templated to work with any scalar type that supports std::isnan and std::max.
template <typename Scalar>
inline Scalar fmax_custom(Scalar val_a, Scalar val_b) {
    static_assert(std::is_floating_point<Scalar>::value,
                  "fmax_custom is intended for floating-point scalar types due to NaN handling.");
    bool a_is_nan = std::isnan(val_a);
    bool b_is_nan = std::isnan(val_b);
    if (a_is_nan && b_is_nan) return std::numeric_limits<Scalar>::quiet_NaN();
    if (a_is_nan) return val_b;
    if (b_is_nan) return val_a;
    return std::max(val_a, val_b);
}

// Min operation for two Eigen arrays
template<typename DerivedA, typename DerivedB>
auto Min(const Eigen::ArrayBase<DerivedA>& a, const Eigen::ArrayBase<DerivedB>& b) {
    assert(a.rows() == b.rows() && "Min(array, array): a and b row counts differ.");
    assert(a.cols() == b.cols() && "Min(array, array): a and b column counts differ.");
    using ScalarA = typename DerivedA::Scalar;
    using ScalarB = typename DerivedB::Scalar;
    static_assert(std::is_same<ScalarA, ScalarB>::value, "Min(array, array): a and b must have the same scalar type.");
    static_assert(std::is_floating_point<ScalarA>::value, "Min(array, array) requires floating-point scalar types.");

    return a.binaryExpr(b, [](ScalarA val_a, ScalarA val_b) {
        return fmin_custom(val_a, val_b);
    });
}

// Min operation for an Eigen array and a scalar
template<typename DerivedA>
auto Min(const Eigen::ArrayBase<DerivedA>& a, typename DerivedA::Scalar b_scalar) {
    using ScalarA = typename DerivedA::Scalar;
    static_assert(std::is_floating_point<ScalarA>::value, "Min(array, scalar) requires floating-point scalar types.");

    // Eigen's unaryExpr can be used with a lambda capturing the scalar.
    // The scalar b_scalar will be passed to each invocation of the lambda.
    return a.unaryExpr([b_scalar](ScalarA val_a) {
        return fmin_custom(val_a, b_scalar);
    });
}

// Min operation for a scalar and an Eigen array
template<typename DerivedB>
auto Min(typename DerivedB::Scalar a_scalar, const Eigen::ArrayBase<DerivedB>& b) {
    using ScalarB = typename DerivedB::Scalar;
    static_assert(std::is_floating_point<ScalarB>::value, "Min(scalar, array) requires floating-point scalar types.");

    // Apply unaryExpr on b, capturing a_scalar
    return b.unaryExpr([a_scalar](ScalarB val_b) {
        return fmin_custom(a_scalar, val_b);
    });
}

// Max operation for two Eigen arrays
template<typename DerivedA, typename DerivedB>
auto Max(const Eigen::ArrayBase<DerivedA>& a, const Eigen::ArrayBase<DerivedB>& b) {
    assert(a.rows() == b.rows() && "Max(array, array): a and b row counts differ.");
    assert(a.cols() == b.cols() && "Max(array, array): a and b column counts differ.");
    using ScalarA = typename DerivedA::Scalar;
    using ScalarB = typename DerivedB::Scalar;
    static_assert(std::is_same<ScalarA, ScalarB>::value, "Max(array, array): a and b must have the same scalar type.");
    static_assert(std::is_floating_point<ScalarA>::value, "Max(array, array) requires floating-point scalar types.");

    return a.binaryExpr(b, [](ScalarA val_a, ScalarA val_b) {
        return fmax_custom(val_a, val_b);
    });
}

// Max operation for an Eigen array and a scalar
template<typename DerivedA>
auto Max(const Eigen::ArrayBase<DerivedA>& a, typename DerivedA::Scalar b_scalar) {
    using ScalarA = typename DerivedA::Scalar;
    static_assert(std::is_floating_point<ScalarA>::value, "Max(array, scalar) requires floating-point scalar types.");

    return a.unaryExpr([b_scalar](ScalarA val_a) {
        return fmax_custom(val_a, b_scalar);
    });
}

// Max operation for a scalar and an Eigen array
template<typename DerivedB>
auto Max(typename DerivedB::Scalar a_scalar, const Eigen::ArrayBase<DerivedB>& b) {
    using ScalarB = typename DerivedB::Scalar;
    static_assert(std::is_floating_point<ScalarB>::value, "Max(scalar, array) requires floating-point scalar types.");

    return b.unaryExpr([a_scalar](ScalarB val_b) {
        return fmax_custom(a_scalar, val_b);
    });
}

} // namespace feature_operators
