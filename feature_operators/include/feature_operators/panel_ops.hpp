#ifndef FEATURE_OPERATORS_PANEL_OPS_HPP
#define FEATURE_OPERATORS_PANEL_OPS_HPP

#include "feature_operators/types.hpp" // Added
#include <Eigen/Dense> // Kept for other Eigen types

namespace feature_operators {

// Using a slightly larger epsilon for clamping to avoid issues at the exact boundaries 0 and 1
static constexpr double CLAMP_EPSILON = 1e-9; 

DataFrame pn_Mean(const DataFrame& data);
DataFrame pn_Rank2(const DataFrame& data);
// Helper function for pn_Rank (and now pn_GroupRank)
Eigen::ArrayXd rank_row_custom_norm(const Eigen::ArrayXd& row_data); 
DataFrame pn_Rank(const DataFrame& data);
DataFrame pn_Cut(const DataFrame& data, double lower_bound_pct = 20.0, double upper_bound_pct = 80.0);
DataFrame pn_TransNorm(const DataFrame& data);

// Calculates the cross-fit alpha and residuals.
DataFrame pn_CrossFit(const DataFrame& x, const DataFrame& y);

// Centers the rank of each element in each row.
DataFrame pn_RankCentered(const DataFrame& data);

// Replicates the row-wise maximum across each row.
DataFrame pn_FillMax(const DataFrame& data);

// Replicates the row-wise minimum across each row.
DataFrame pn_FillMin(const DataFrame& data);

// Winsorizes data row-wise based on standard deviation.
DataFrame pn_Winsor(const DataFrame& data, double multiplier);

// Standardizes data row-wise (data - mean) / std.
DataFrame pn_TransStd(const DataFrame& data);

// Standardizes data row-wise (data - median) / std.
DataFrame pn_Stand(const DataFrame& data);

} // namespace feature_operators
#endif // FEATURE_OPERATORS_PANEL_OPS_HPP