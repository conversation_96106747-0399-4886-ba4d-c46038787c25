#ifndef FEATURE_OPERATORS_TIMESERIES_OPS_HPP
#define FEATURE_OPERATORS_TIMESERIES_OPS_HPP

#include "feature_operators/types.hpp" // Added
#include <Eigen/Dense> // Kept for other Eigen types

namespace feature_operators {

DataFrame ts_Delay(const DataFrame& data, int n);
DataFrame ts_Mean(const DataFrame& data, int n);
DataFrame ts_Stdev(const DataFrame& data, int n);
DataFrame ts_Delta(const DataFrame& data, int n);
DataFrame ts_Min(const DataFrame& data, int n);
DataFrame ts_Max(const DataFrame& data, int n);

// Calculates rolling skewness for each column.
DataFrame ts_Skewness(const DataFrame& data, int n);

// Calculates rolling Fisher kurtosis for each column.
DataFrame ts_Kurtosis(const DataFrame& data, int n);

// Scales data column-wise in a rolling window: (value - min) / (max - min).
DataFrame ts_Scale(const DataFrame& data, int n);

// Calculates rolling correlation between corresponding columns of two DataFrames.
DataFrame ts_Corr(const DataFrame& data1, const DataFrame& data2, int n);

// Calculates rolling covariance between corresponding columns of two DataFrames.
DataFrame ts_Cov(const DataFrame& data1, const DataFrame& data2, int n);

// Performs rolling regression and returns beta, alpha, fitted values, or residuals.
DataFrame ts_Regression(const DataFrame& data1, const DataFrame& data2, int n, char rettype);

// Calculates rolling entropy.
DataFrame ts_Entropy(const DataFrame& data, int n, int bucket = 10);

// Calculates rolling partial correlation.
DataFrame ts_Partial_corr(const DataFrame& data1, const DataFrame& data2, const DataFrame& data3, int n);

// Applies rolling rank, centering, and inverse normal CDF transformation.
DataFrame ts_TransNorm(const DataFrame& data, int n);

// Calculates rolling linearly weighted average.
DataFrame ts_Decay(const DataFrame& data, int n);

// Calculates rolling exponentially weighted average.
DataFrame ts_Decay2(const DataFrame& data, int n);

// Calculates rolling percentile rank and centers it.
DataFrame ts_Rank(const DataFrame& data, int n);

// Calculates rolling median.
DataFrame ts_Median(const DataFrame& data, int n);

// Calculates rolling argmax (1-based index from end of window).
DataFrame ts_Argmax(const DataFrame& data, int n);

// Calculates rolling argmin (1-based index from end of window).
DataFrame ts_Argmin(const DataFrame& data, int n);

// Calculates rolling sum.
DataFrame ts_Sum(const DataFrame& data, int n);

// Calculates rolling product (NaNs as 1).
DataFrame ts_Product(const DataFrame& data, int n);

// Calculates data divided by its n-period lagged value.
DataFrame ts_Divide(const DataFrame& data, int n);

// Calculates rate of change: data / data_lagged_n - 1.
DataFrame ts_ChgRate(const DataFrame& data, int n);

// Calculates rolling max drawdown.
DataFrame ts_MaxDD(const DataFrame& data, int n);

// Calculates rolling quantile.
DataFrame ts_Quantile(const DataFrame& data, int n, char rettype);

// Calculates difference between rolling mean and rolling linear decay mean.
DataFrame ts_MeanChg(const DataFrame& data, int n);

// Tot_ series wrapper functions (fixed window of 15)
DataFrame Tot_Mean(const DataFrame& data);
DataFrame Tot_Sum(const DataFrame& data);
DataFrame Tot_Stdev(const DataFrame& data);
DataFrame Tot_Delta(const DataFrame& data);
DataFrame Tot_Divide(const DataFrame& data);
DataFrame Tot_ChgRate(const DataFrame& data);
DataFrame Tot_Rank(const DataFrame& data);
DataFrame Tot_Min(const DataFrame& data);
DataFrame Tot_Max(const DataFrame& data);
DataFrame Tot_ArgMax(const DataFrame& data);
DataFrame Tot_ArgMin(const DataFrame& data);

} // namespace feature_operators
#endif // FEATURE_OPERATORS_TIMESERIES_OPS_HPP
