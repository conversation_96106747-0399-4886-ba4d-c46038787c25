// #ifndef FEATURE_OPERATORS_DATA_UTILS_HPP
// #define FEATURE_OPERATORS_DATA_UTILS_HPP

// #include "feature_operators/types.hpp" // Added
// #include <Eigen/Dense> // Kept for other Eigen types

// namespace feature_operators {

// DataFrame FilterInf(const DataFrame& a);
// DataFrame FillNan(const DataFrame& data, double fill_value);
// DataFrame FillNan(const DataFrame& data, const DataFrame& fill_data);
// DataFrame getNan(const DataFrame& a);
// DataFrame getInf(const DataFrame& a);

// // Selects values from 'aa' based on shifted indices in 'idxx'.
// DataFrame GetSingleBar(const DataFrame& aa, const DataFrame& idxx);

// } // namespace feature_operators
// #endif // FEATURE_OPERATORS_DATA_UTILS_HPP

#include "feature_operators/types.hpp"      // For DataFrame (assuming it's defined here, e.g., as Eigen::ArrayXXd)
#include "feature_operators/timeseries_ops.hpp" // For ts_Delay
#include <Eigen/Dense>                       // For Eigen::ArrayBase, Eigen::Array, etc.
#include <cassert>                           // For assert
#include <limits>                            // For std::numeric_limits
#include <cmath>                             // For std::isnan, std::isinf, std::floor

// Assuming DataFrame is defined in "feature_operators/types.hpp"
// For example: using DataFrame = Eigen::Array<double, Eigen::Dynamic, Eigen::Dynamic>;
// We will use 'DataFrame' when a concrete matrix type is explicitly needed for return or storage.
// We use 'typename Derived::Scalar' for generality within templates.

namespace feature_operators {

/**
 * @brief Filters infinity values, replacing them with NaN.
 * Returns an Eigen expression template.
 */
template<typename Derived>
auto FilterInf(const Eigen::ArrayBase<Derived>& a) {
  return a.unaryExpr([](typename Derived::Scalar x) {
    if (std::isinf(x)) {
      return std::numeric_limits<typename Derived::Scalar>::quiet_NaN();
    }
    return x;
  });
}

/**
 * @brief Fills NaN values in 'data' with a specified 'fill_value'.
 * Returns an Eigen expression template.
 */
template<typename Derived>
auto FillNan(const Eigen::ArrayBase<Derived>& data, typename Derived::Scalar fill_value) {
  // data.array() ensures we are using array-specific methods like isNaN().
  // If Derived is already an Eigen::Array, .array() is a no-op.
  return (data.array().isNaN()).select(fill_value, data);
}

/**
 * @brief Fills NaN values in 'data' with corresponding values from 'fill_data'.
 * Returns an Eigen expression template.
 */
template<typename DerivedData, typename DerivedFill>
auto FillNan(const Eigen::ArrayBase<DerivedData>& data,
             const Eigen::ArrayBase<DerivedFill>& fill_data) {
  assert(data.rows() == fill_data.rows() && data.cols() == fill_data.cols() &&
         "FillNan: data and fill_data must have the same dimensions.");
  return (data.array().isNaN()).select(fill_data, data);
}

/**
 * @brief Creates a DataFrame of NaNs with the same dimensions as 'exemplar'.
 * The input 'exemplar' is used only for its dimensions.
 * Returns a concrete DataFrame.
 */
template<typename DerivedExemplar>
auto getNan(const Eigen::ArrayBase<DerivedExemplar>& exemplar) -> Eigen::Array<typename DerivedExemplar::Scalar, Eigen::Dynamic, Eigen::Dynamic> {
    using Scalar = typename DerivedExemplar::Scalar;
    return Eigen::Array<Scalar, Eigen::Dynamic, Eigen::Dynamic>::Constant(
        exemplar.rows(), exemplar.cols(), std::numeric_limits<Scalar>::quiet_NaN());
}

/**
 * @brief Creates a DataFrame of Infs with the same dimensions as 'exemplar'.
 * The input 'exemplar' is used only for its dimensions.
 * Returns a concrete DataFrame.
 */
template<typename DerivedExemplar>
auto getInf(const Eigen::ArrayBase<DerivedExemplar>& exemplar) -> Eigen::Array<typename DerivedExemplar::Scalar, Eigen::Dynamic, Eigen::Dynamic> {
    using Scalar = typename DerivedExemplar::Scalar;
    return Eigen::Array<Scalar, Eigen::Dynamic, Eigen::Dynamic>::Constant(
        exemplar.rows(), exemplar.cols(), std::numeric_limits<Scalar>::infinity());
}


/**
 * @brief Selects elements from 'aa' based on index values in 'idxx'.
 * For each element in 'out', out(r,c) = aa_delayed(r,c) if floor(idxx(r,c)) - 1 == current_delay_step.
 * This function iteratively builds the result and returns a concrete DataFrame.
 * Internal calculations leverage expression templates for efficiency.
 */
template<typename DerivedA, typename DerivedIdx>
auto GetSingleBar(const Eigen::ArrayBase<DerivedA>& aa, const Eigen::ArrayBase<DerivedIdx>& idxx) -> Eigen::Array<typename DerivedA::Scalar, Eigen::Dynamic, Eigen::Dynamic> {
  assert(aa.rows() == idxx.rows() &&
         "GetSingleBar: aa and idxx must have the same number of rows.");
  assert(aa.cols() == idxx.cols() &&
         "GetSingleBar: aa and idxx must have the same number of columns.");

  using Scalar = typename DerivedA::Scalar; // Assumes aa and idxx have compatible scalar types.
  using ConcreteDataFrame = Eigen::Array<Scalar, Eigen::Dynamic, Eigen::Dynamic>;

  const Scalar NaN_val = std::numeric_limits<Scalar>::quiet_NaN();
  ConcreteDataFrame out = ConcreteDataFrame::Constant(aa.rows(), aa.cols(), NaN_val);

  // Determine max_iter_val from idxx.
  // Coefficient-wise access idxx(r,c) will evaluate that part of the expression if idxx is one.
  Scalar max_val_in_idxx = -std::numeric_limits<Scalar>::infinity();
  bool found_finite_in_idxx = false;
  for (Eigen::Index r = 0; r < idxx.rows(); ++r) {
    for (Eigen::Index c = 0; c < idxx.cols(); ++c) {
      Scalar val = idxx(r, c);
      if (std::isfinite(val)) {
        if (!found_finite_in_idxx || val > max_val_in_idxx) {
          max_val_in_idxx = val;
        }
        found_finite_in_idxx = true;
      }
    }
  }

  int max_iter_val = 0;
  if (found_finite_in_idxx && max_val_in_idxx >= static_cast<Scalar>(1.0)) {
    max_iter_val = static_cast<int>(std::floor(max_val_in_idxx));
  }

  if (max_iter_val <= 0) {
    return out; // Return the NaN-filled matrix
  }

  // 'aa' can be an expression. If 'ts_Delay' expects a concrete matrix,
  // 'aa' would be evaluated upon calling 'ts_Delay'. If 'ts_Delay' is
  // also templated to accept Eigen::ArrayBase and returns an expression,
  // the chain of expressions continues.
  for (int v_loop_idx = 0; v_loop_idx < max_iter_val; ++v_loop_idx) {
    int current_shift = v_loop_idx;
    Scalar v_comparison_val = static_cast<Scalar>(v_loop_idx);

    // 'Tmp_delayed' will be an expression if ts_Delay returns one, or a concrete matrix otherwise.
    auto Tmp_delayed = feature_operators::ts_Delay(aa, current_shift);

    // Create a boolean condition mask expression using unaryExpr.
    // The lambda captures v_comparison_val by value.
    auto condition_mask_expr = idxx.unaryExpr(
        [v_comparison_val](Scalar idx_val) -> bool { // Using Scalar from DerivedIdx
            return std::isfinite(idx_val) && (std::floor(idx_val) - static_cast<Scalar>(1.0) == v_comparison_val);
        }
    );

    // Update 'out' using Eigen's select operation.
    // This efficiently combines the condition, the (possibly expression) temporary delayed values,
    // and the current state of 'out'.
    // out(coeff) = condition_mask_expr(coeff) ? Tmp_delayed(coeff) : out(coeff)
    out = condition_mask_expr.select(Tmp_delayed, out);
  }
  return out;
}

} // namespace feature_operators