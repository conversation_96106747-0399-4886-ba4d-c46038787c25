#include "feature_operators/data_utils.hpp"
#include "feature_operators/types.hpp" // Added
#include <catch2/catch_test_macros.hpp>
#include <catch2/matchers/catch_matchers_floating_point.hpp> // For Approx
#include <Eigen/Dense> // Kept
#include <limits> // For std::numeric_limits
#include <cmath>  // For std::isnan

using feature_operators::DataFrame;

TEST_CASE("DataUtils: FilterInfVal function", "[DataUtils]") {
    DataFrame a(1, 4);
    const double inf = std::numeric_limits<double>::infinity();
    const double nan_val = std::numeric_limits<double>::quiet_NaN();
    a << 1.0, inf, -inf, 4.0;
    
    DataFrame result = feature_operators::FilterInf(a);
    
    REQUIRE_THAT(result(0,0), Catch::Matchers::WithinAbs(1.0, 1e-9));
    REQUIRE(std::isnan(result(0,1)));
    REQUIRE(std::isnan(result(0,2)));
    REQUIRE_THAT(result(0,3), Catch::Matchers::WithinAbs(4.0, 1e-9));
}

TEST_CASE("DataUtils: FillNanVal (scalar) function", "[DataUtils]") {
    DataFrame a(1, 4);
    const double nan_val = std::numeric_limits<double>::quiet_NaN();
    a << 1.0, nan_val, 3.0, nan_val;
    double fill_value = 99.0;
    
    DataFrame result = feature_operators::FillNan(a, fill_value);
    
    REQUIRE_THAT(result(0,0), Catch::Matchers::WithinAbs(1.0, 1e-9));
    REQUIRE_THAT(result(0,1), Catch::Matchers::WithinAbs(fill_value, 1e-9));
    REQUIRE_THAT(result(0,2), Catch::Matchers::WithinAbs(3.0, 1e-9));
    REQUIRE_THAT(result(0,3), Catch::Matchers::WithinAbs(fill_value, 1e-9));
}

TEST_CASE("DataUtils: FillNanVal (array) function", "[DataUtils]") {
    DataFrame data_df(2, 2); // Renamed to avoid conflict with namespace
    const double nan_val = std::numeric_limits<double>::quiet_NaN();
    data_df << 1.0, nan_val, nan_val, 4.0;
    
    DataFrame fill_data(2, 2);
    fill_data << 10.0, 20.0, 30.0, 40.0;
    
    DataFrame expected(2, 2);
    expected << 1.0, 20.0, 30.0, 4.0;
    
    DataFrame result = feature_operators::FillNan(data_df, fill_data);
    
    REQUIRE((result - expected).abs().maxCoeff() < 1e-9);

    // Test assertion for mismatched sizes (optional, as it's an assert)
    // DataFrame wrong_size_fill(1,1);
    // wrong_size_fill << 100.0;
    // REQUIRE_THROWS(feature_operators::fill_nan_val(data_df, wrong_size_fill)); 
}

TEST_CASE("DataUtils: GetNanLike function", "[DataUtils]") {
    DataFrame a(2, 3);
    a << 1, 2, 3, 4, 5, 6; // Content doesn't matter, only shape
    DataFrame result = feature_operators::getNan(a);
    REQUIRE(result.rows() == 2);
    REQUIRE(result.cols() == 3);
    for (int r = 0; r < result.rows(); ++r) {
        for (int c = 0; c < result.cols(); ++c) {
            REQUIRE(std::isnan(result(r,c)));
        }
    }
}

// Helper for comparing Eigen arrays with NaNs, can be reused
void require_dataframes_equal_with_nans(const DataFrame& result, const DataFrame& expected, double tolerance = 1e-9) {
    REQUIRE(result.rows() == expected.rows());
    REQUIRE(result.cols() == expected.cols());
    for (Eigen::Index r = 0; r < result.rows(); ++r) {
        for (Eigen::Index c = 0; c < result.cols(); ++c) {
            if (std::isnan(expected(r, c))) {
                REQUIRE(std::isnan(result(r, c)));
            } else {
                REQUIRE_THAT(result(r, c), Catch::Matchers::WithinAbs(expected(r, c), tolerance));
            }
        }
    }
}

TEST_CASE("DataUtils: GetSingleBar", "[DataUtils]") {
    const double NaN = std::numeric_limits<double>::quiet_NaN();

    SECTION("Basic Case") {
        DataFrame aa(3, 3);
        aa << 1, 2, 3,
              4, 5, 6,
              7, 8, 9;
        DataFrame idxx(3, 3);
        idxx << 1, 1, 1,  // Select from aa.shift(0)
                2, 2, 2,  // Select from aa.shift(1)
                3, 3, 3;  // Select from aa.shift(2)
        
        DataFrame result = feature_operators::GetSingleBar(aa, idxx);
        DataFrame expected(3, 3);
        // v_loop_idx = 0 (current_shift = 0): idxx-1 == 0. Tmp = aa
        //   Row 0: idxx-1 = [0,0,0]. out(0,c) = aa(0,c) => [1,2,3]
        // v_loop_idx = 1 (current_shift = 1): idxx-1 == 1. Tmp = aa.shift(1)
        //   Row 1: idxx-1 = [1,1,1]. out(1,c) = Tmp(1,c) = aa(0,c) => [1,2,3]
        // v_loop_idx = 2 (current_shift = 2): idxx-1 == 2. Tmp = aa.shift(2)
        //   Row 2: idxx-1 = [2,2,2]. out(2,c) = Tmp(2,c) = aa(0,c) => [1,2,3]
        expected << 1,2,3,
                    1,2,3, // aa delayed by 1, so aa(0,:)
                    1,2,3; // aa delayed by 2, so aa(0,:)
        
        // Let's re-trace logic carefully:
        // max_iter_val = floor(3) = 3. Loop v_loop_idx = 0, 1, 2.
        //
        // v_loop_idx = 0: current_shift = 0. Tmp = aa.
        //   idxx(0,0)=1. floor(1)-1 = 0 == v_loop_idx. out(0,0) = Tmp(0,0) = 1.
        //   idxx(0,1)=1. floor(1)-1 = 0 == v_loop_idx. out(0,1) = Tmp(0,1) = 2.
        //   idxx(0,2)=1. floor(1)-1 = 0 == v_loop_idx. out(0,2) = Tmp(0,2) = 3.
        //
        // v_loop_idx = 1: current_shift = 1. Tmp = ts_Delay(aa,1).
        //   Tmp = [[NaN,NaN,NaN], [1,2,3], [4,5,6]]
        //   idxx(1,0)=2. floor(2)-1 = 1 == v_loop_idx. out(1,0) = Tmp(1,0) = 1.
        //   idxx(1,1)=2. floor(2)-1 = 1 == v_loop_idx. out(1,1) = Tmp(1,1) = 2.
        //   idxx(1,2)=2. floor(2)-1 = 1 == v_loop_idx. out(1,2) = Tmp(1,2) = 3.
        //
        // v_loop_idx = 2: current_shift = 2. Tmp = ts_Delay(aa,2).
        //   Tmp = [[NaN,NaN,NaN], [NaN,NaN,NaN], [1,2,3]]
        //   idxx(2,0)=3. floor(3)-1 = 2 == v_loop_idx. out(2,0) = Tmp(2,0) = 1.
        //   idxx(2,1)=3. floor(3)-1 = 2 == v_loop_idx. out(2,1) = Tmp(2,1) = 2.
        //   idxx(2,2)=3. floor(3)-1 = 2 == v_loop_idx. out(2,2) = Tmp(2,2) = 3.
        // Other elements of 'out' remain NaN.
        expected << 1,2,3,
                    1,2,3,
                    1,2,3;
        require_dataframes_equal_with_nans(result, expected);
    }

    SECTION("NaN Handling in aa") {
        DataFrame aa(3, 2);
        aa << 1.0, NaN,
              NaN, 4.0,
              5.0, 6.0;
        DataFrame idxx(3, 2);
        idxx << 1.0, 2.0, // out(0,0)=aa(0,0)=1. out(0,1)=ts_Delay(aa,1)(0,1)=NaN
                1.0, 2.0, // out(1,0)=aa(1,0)=NaN. out(1,1)=ts_Delay(aa,1)(1,1)=aa(0,1)=NaN
                3.0, 1.0; // out(2,0)=ts_Delay(aa,2)(2,0)=aa(0,0)=1. out(2,1)=aa(2,1)=6
        
        DataFrame result = feature_operators::GetSingleBar(aa, idxx);
        DataFrame expected(3,2);
        // v_loop_idx = 0 (shift 0), Tmp = aa
        //  idxx(0,0)=1 -> out(0,0)=Tmp(0,0)=1
        //  idxx(1,0)=1 -> out(1,0)=Tmp(1,0)=NaN
        //  idxx(2,1)=1 -> out(2,1)=Tmp(2,1)=6
        // v_loop_idx = 1 (shift 1), Tmp = [[NaN,NaN],[1,NaN],[NaN,4]]
        //  idxx(0,1)=2 -> out(0,1)=Tmp(0,1)=NaN
        //  idxx(1,1)=2 -> out(1,1)=Tmp(1,1)=NaN
        // v_loop_idx = 2 (shift 2), Tmp = [[NaN,NaN],[NaN,NaN],[1,NaN]]
        //  idxx(2,0)=3 -> out(2,0)=Tmp(2,0)=1
        expected << 1.0, NaN,
                    NaN, NaN,
                    1.0, 6.0;
        require_dataframes_equal_with_nans(result, expected);
    }

    SECTION("NaN Handling in idxx") {
        DataFrame aa(2,2); aa << 1,2,3,4;
        DataFrame idxx_nan(2,2); idxx_nan << 1.0, NaN, NaN, 2.0;
        DataFrame result_nan_idxx = feature_operators::GetSingleBar(aa, idxx_nan);
        DataFrame expected_nan_idxx(2,2);
        // max_iter_val = floor(2)=2. Loop v_loop_idx = 0,1
        // v_loop_idx = 0 (shift 0), Tmp = aa
        //  idxx(0,0)=1. floor(1)-1=0. out(0,0)=Tmp(0,0)=1
        //  idxx(0,1)=NaN. skip.
        //  idxx(1,0)=NaN. skip.
        // v_loop_idx = 1 (shift 1), Tmp = [[NaN,NaN],[1,2]]
        //  idxx(1,1)=2. floor(2)-1=1. out(1,1)=Tmp(1,1)=2
        expected_nan_idxx << 1.0, NaN, NaN, 2.0;
        require_dataframes_equal_with_nans(result_nan_idxx, expected_nan_idxx);

        DataFrame idxx_all_nan(2,2); idxx_all_nan << NaN,NaN,NaN,NaN;
        DataFrame result_all_nan_idxx = feature_operators::GetSingleBar(aa, idxx_all_nan);
        REQUIRE(result_all_nan_idxx.isNaN().all()); // max_iter_val = 0
    }

    SECTION("idxx values leading to no selection or partial selection") {
        DataFrame aa(2,2); aa << 10,20,30,40;
        DataFrame idxx_no_sel(2,2); idxx_no_sel << 5.0, 5.0, 5.0, 5.0; // max_iter_val = 5
        DataFrame result_no_sel = feature_operators::GetSingleBar(aa, idxx_no_sel);
        // Loop v_idx = 0,1,2,3,4. None of idxx-1 will match these.
        REQUIRE(result_no_sel.isNaN().all());

        DataFrame idxx_partial(2,2); idxx_partial << 1.0, 3.0, 1.0, 3.0; // max_iter_val = 3
        DataFrame result_partial = feature_operators::GetSingleBar(aa, idxx_partial);
        DataFrame expected_partial(2,2);
        // v_loop_idx=0: Tmp=aa. out(0,0)=aa(0,0)=10. out(1,0)=aa(1,0)=30
        // v_loop_idx=1: Tmp=ts_Delay(aa,1). No idxx-1 == 1
        // v_loop_idx=2: Tmp=ts_Delay(aa,2). out(0,1)=ts_Delay(aa,2)(0,1)=NaN. out(1,1)=ts_Delay(aa,2)(1,1)=NaN
        expected_partial << 10, NaN, 30, NaN;
        require_dataframes_equal_with_nans(result_partial, expected_partial);
    }
    
    SECTION("idxx max value variations") {
        DataFrame aa(2,1); aa << 10, 20;
        DataFrame idxx0(2,1); idxx0 << 0.0, 0.0; // max_iter_val = 0
        DataFrame result0 = feature_operators::GetSingleBar(aa, idxx0);
        REQUIRE(result0.isNaN().all());

        DataFrame idxx_neg(2,1); idxx_neg << -1.0, -2.0; // max_iter_val = 0
        DataFrame result_neg = feature_operators::GetSingleBar(aa, idxx_neg);
        REQUIRE(result_neg.isNaN().all());
        
        DataFrame idxx1(2,1); idxx1 << 1.0, 1.0; // max_iter_val = 1
        DataFrame result1 = feature_operators::GetSingleBar(aa, idxx1);
        DataFrame expected1(2,1); expected1 << 10, 20; // v_loop_idx=0 -> Tmp=aa. out(0,0)=aa(0,0)=10. out(1,0)=aa(1,0)=20
        require_dataframes_equal_with_nans(result1, expected1);
    }

    SECTION("idxx with decimal values") {
        DataFrame aa(1,2); aa << 100, 200;
        DataFrame idxx_dec(1,2); idxx_dec << 1.2, 1.8; // floor(idxx)-1 -> 0, 0
        DataFrame result_dec = feature_operators::GetSingleBar(aa, idxx_dec);
        DataFrame expected_dec(1,2); expected_dec << 100, 200; // Both select from aa.shift(0)
        require_dataframes_equal_with_nans(result_dec, expected_dec);

        idxx_dec << 2.1, 2.9; // floor(idxx)-1 -> 1, 1
        result_dec = feature_operators::GetSingleBar(aa, idxx_dec);
        // max_iter_val = 2.
        // v_loop_idx=0: No match
        // v_loop_idx=1: Tmp=ts_Delay(aa,1) = [[NaN,NaN]]. out(0,0)=NaN, out(0,1)=NaN
        REQUIRE(result_dec.isNaN().all());
    }
}
TEST_CASE("DataUtils: GetInfLike function", "[DataUtils]") {
    DataFrame a(3, 2);
    // Content doesn't matter, only shape
    DataFrame result = feature_operators::getInf(a);
    REQUIRE(result.rows() == 3);
    REQUIRE(result.cols() == 2);
    const double inf = std::numeric_limits<double>::infinity();
    for (int r = 0; r < result.rows(); ++r) {
        for (int c = 0; c < result.cols(); ++c) {
            REQUIRE(result(r,c) == inf);
        }
    }
}
