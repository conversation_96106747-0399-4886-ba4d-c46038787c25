#!/usr/bin/env python3
"""
比较C++原始版本和重构版本(v2)的性能
Compare C++ V1 vs V2 Performance

用法:
python compare_cpp_v1_v2_performance.py --benchmark-file benchmark_results.json
python compare_cpp_v1_v2_performance.py --output-dir custom_output
"""

import pandas as pd
import numpy as np
import json
import argparse
import time
from pathlib import Path

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='Compare C++ V1 vs V2 Performance')
    parser.add_argument('--benchmark-file', 
                       default="/home/<USER>/git/feature_operators/test_right/test_results/benchmark_cpp_results.json",
                       help='基准测试结果文件')
    parser.add_argument('--output-dir', 
                       default="/home/<USER>/git/feature_operators/tests/test_benchmark/results/comparison",
                       help='输出目录')
    
    args = parser.parse_args()
    
    # 设置路径
    benchmark_file = Path(args.benchmark_file)
    output_dir = Path(args.output_dir)
    
    # 创建输出目录
    output_dir.mkdir(parents=True, exist_ok=True)
    
    if not benchmark_file.exists():
        print(f"Benchmark results file not found: {benchmark_file}")
        print("请先运行基准测试生成性能数据")
        return 1
    
    # 需要比较的算子列表
    operators_to_compare = [
        "ts_Delay", "ts_Mean", "ts_Sum", "ts_Stdev", "ts_Min", "ts_Max",
        "ts_Delta", "ts_Divide", "ts_ChgRate", "ts_Argmax", "ts_Argmin",
        "ts_Rank", "ts_Median", "ts_Corr", "ts_Cov", "ts_Skewness",
        "ts_Kurtosis", "ts_Scale", "ts_Product", "ts_Decay", "ts_Decay2",
        "ts_MaxDD", "ts_MeanChg", "ts_Quantile_A", "ts_Quantile_B",
        "ts_Quantile_C", "ts_Quantile_D"
    ]
    
    tot_operators = [
        "Tot_Mean", "Tot_Sum", "Tot_Stdev", "Tot_Delta", "Tot_Divide",
        "Tot_ChgRate", "Tot_Rank", "Tot_ArgMax", "Tot_ArgMin", "Tot_Max", "Tot_Min"
    ]
    
    # 合并所有算子
    all_operators = operators_to_compare + tot_operators
    
    print("=" * 80)
    print("C++ 性能比较报告 (原始版本 vs 重构版本v2)")
    print("=" * 80)
    print(f"基准测试文件: {benchmark_file}")
    print("=" * 80)
    
    with open(benchmark_file, 'r') as f:
        benchmark_data = json.load(f)
    
    print(f"{'算子名称':<20} {'原版本(μs)':<15} {'v2版本(μs)':<15} {'性能提升':<15} {'提升率':<10}")
    print("-" * 80)
    
    performance_improvements = []
    comparison_results = {}
    
    for op in all_operators:
        v1_key = op
        v2_key = f"{op}_v2"
        
        if v1_key in benchmark_data and v2_key in benchmark_data:
            v1_time = benchmark_data[v1_key]
            v2_time = benchmark_data[v2_key]
            
            if v1_time > 0:
                improvement = (v1_time - v2_time) / v1_time * 100
                performance_improvements.append(improvement)
                
                if improvement > 5:
                    improvement_str = f"+{improvement:.1f}%"
                    status = "✓ 提升"
                elif improvement < -5:  # 性能下降超过5%
                    improvement_str = f"{improvement:.1f}%"
                    status = "✗ 下降"
                else:
                    improvement_str = f"{improvement:.1f}%"
                    status = "≈ 相近"
                
                print(f"{op:<20} {v1_time:<15.3f} {v2_time:<15.3f} {improvement_str:<15} {status:<10}")
                
                comparison_results[op] = {
                    "v1_time": v1_time,
                    "v2_time": v2_time,
                    "improvement_percent": improvement,
                    "status": status
                }
            else:
                print(f"{op:<20} {'N/A':<15} {'N/A':<15} {'N/A':<15} {'错误':<10}")
                comparison_results[op] = {
                    "v1_time": v1_time,
                    "v2_time": v2_time,
                    "improvement_percent": None,
                    "status": "错误"
                }
        else:
            missing = []
            if v1_key not in benchmark_data:
                missing.append("v1")
            if v2_key not in benchmark_data:
                missing.append("v2")
            
            print(f"{op:<20} {'缺失':<15} {'缺失':<15} {'N/A':<15} {f'缺失{missing}':<10}")
            comparison_results[op] = {
                "v1_time": None,
                "v2_time": None,
                "improvement_percent": None,
                "status": f"缺失{missing}"
            }
    
    print("-" * 80)
    
    # 生成统计报告
    timestamp = time.strftime("%Y%m%d_%H%M%S")
    
    if performance_improvements:
        avg_improvement = np.mean(performance_improvements)
        median_improvement = np.median(performance_improvements)
        max_improvement = np.max(performance_improvements)
        min_improvement = np.min(performance_improvements)
        
        print(f"性能统计:")
        print(f"  平均性能提升: {avg_improvement:.1f}%")
        print(f"  中位数性能提升: {median_improvement:.1f}%")
        print(f"  最大性能提升: {max_improvement:.1f}%")
        print(f"  最小性能提升: {min_improvement:.1f}%")
        
        # 统计提升、下降、相近的算子数量
        improved_count = sum(1 for x in performance_improvements if x > 5)
        degraded_count = sum(1 for x in performance_improvements if x < -5)
        similar_count = sum(1 for x in performance_improvements if -5 <= x <= 5)
        
        print(f"\n性能分布:")
        print(f"  性能提升 (>5%): {improved_count} 个算子")
        print(f"  性能下降 (<-5%): {degraded_count} 个算子")
        print(f"  性能相近 (-5%~5%): {similar_count} 个算子")
        
        # 保存详细结果
        results_file = output_dir / f"v1_vs_v2_performance_comparison_{timestamp}.json"
        with open(results_file, 'w') as f:
            json.dump({
                "timestamp": timestamp,
                "benchmark_file": str(benchmark_file),
                "summary": {
                    "avg_improvement": avg_improvement,
                    "median_improvement": median_improvement,
                    "max_improvement": max_improvement,
                    "min_improvement": min_improvement,
                    "improved_count": improved_count,
                    "degraded_count": degraded_count,
                    "similar_count": similar_count,
                    "total_compared": len(performance_improvements)
                },
                "detailed_results": comparison_results
            }, f, indent=2, default=str)
        
        # 生成文本报告
        report_file = output_dir / f"v1_vs_v2_performance_report_{timestamp}.txt"
        with open(report_file, 'w') as f:
            f.write("C++ 性能比较报告 (原始版本 vs 重构版本v2)\n")
            f.write("=" * 60 + "\n\n")
            f.write(f"测试时间: {timestamp}\n")
            f.write(f"基准测试文件: {benchmark_file}\n\n")
            
            f.write("性能统计:\n")
            f.write(f"  平均性能提升: {avg_improvement:.1f}%\n")
            f.write(f"  中位数性能提升: {median_improvement:.1f}%\n")
            f.write(f"  最大性能提升: {max_improvement:.1f}%\n")
            f.write(f"  最小性能提升: {min_improvement:.1f}%\n\n")
            
            f.write("性能分布:\n")
            f.write(f"  性能提升 (>5%): {improved_count} 个算子\n")
            f.write(f"  性能下降 (<-5%): {degraded_count} 个算子\n")
            f.write(f"  性能相近 (-5%~5%): {similar_count} 个算子\n\n")
            
            f.write("详细结果:\n")
            f.write("-" * 60 + "\n")
            f.write(f"{'算子名称':<20} {'原版本(μs)':<15} {'v2版本(μs)':<15} {'提升率':<10}\n")
            f.write("-" * 60 + "\n")
            
            for op, result in comparison_results.items():
                if result["improvement_percent"] is not None:
                    f.write(f"{op:<20} {result['v1_time']:<15.3f} {result['v2_time']:<15.3f} {result['improvement_percent']:<10.1f}%\n")
                else:
                    f.write(f"{op:<20} {'N/A':<15} {'N/A':<15} {'N/A':<10}\n")
        
        print(f"\n详细结果已保存到: {results_file}")
        print(f"文本报告已保存到: {report_file}")
    else:
        print("没有找到可比较的性能数据")
        return 1
    
    print("\nC++ 性能比较完成!")
    return 0

if __name__ == "__main__":
    import sys
    sys.exit(main())
