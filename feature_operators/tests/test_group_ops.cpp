#include "feature_operators/group_ops.hpp"
#include "feature_operators/types.hpp" 
#include <catch2/catch_test_macros.hpp>
#include <catch2/matchers/catch_matchers_floating_point.hpp> // For Approx
#include <Eigen/Dense> 
#include <limits> // For std::numeric_limits
#include <cmath>  // For std::isnan
#include <boost/math/distributions/normal.hpp> // Added for pn_group_norm test

using feature_operators::DataFrame;

const double NaN = std::numeric_limits<double>::quiet_NaN();

// Helper for comparing Eigen arrays with NaNs
void require_eigen_arrays_are_equal(const DataFrame& result, const DataFrame& expected, double tolerance = 1e-9) {
    REQUIRE(result.rows() == expected.rows());
    REQUIRE(result.cols() == expected.cols());
    for (Eigen::Index r = 0; r < result.rows(); ++r) {
        for (Eigen::Index c = 0; c < result.cols(); ++c) {
            if (std::isnan(expected(r, c))) {
                REQUIRE(std::isnan(result(r, c)));
            } else {
                REQUIRE_THAT(result(r, c), Catch::Matchers::WithinAbs(expected(r, c), tolerance));
            }
        }
    }
}

TEST_CASE("GroupOps: pn_group_neutral", "[GroupOps]") {
    DataFrame data(2, 5);
    data << 1.0, 2.0, 3.0, 4.0, 5.0,    // Row 0
            10.0, 20.0, NaN, 40.0, 50.0;   // Row 1

    DataFrame labels(2, 5);
    labels << 1.0, 1.0, 2.0, 2.0, 1.0,  // Row 0: G1={1,2,5}, G2={3,4}
              1.0, 2.0, 1.0, 2.0, NaN; // Row 1: G1={10}, G2={20,40}, (50 is NaN label) -> data(1,4) result NaN

    DataFrame result = feature_operators::pn_GroupNeutral(data, labels);
    DataFrame expected(2, 5);

    // Row 0:
    // Group 1 (label 1.0): data [1,2,5], mean = (1+2+5)/3 = 8/3 = 2.666...
    // Neutral: [1-8/3, 2-8/3, 5-8/3] = [-5/3, -2/3, 7/3] = [-1.666..., -0.666..., 2.333...]
    // Group 2 (label 2.0): data [3,4], mean = (3+4)/2 = 3.5
    // Neutral: [3-3.5, 4-3.5] = [-0.5, 0.5]
    expected(0,0) = 1.0 - (1.0+2.0+5.0)/3.0; 
    expected(0,1) = 2.0 - (1.0+2.0+5.0)/3.0;
    expected(0,2) = 3.0 - (3.0+4.0)/2.0;
    expected(0,3) = 4.0 - (3.0+4.0)/2.0;
    expected(0,4) = 5.0 - (1.0+2.0+5.0)/3.0;

    // Row 1:
    // Group 1 (label 1.0): data [10, NaN]. Valid data [10]. Mean = 10.
    // Neutral: [10-10] = [0]
    // Group 2 (label 2.0): data [20, 40]. Mean = (20+40)/2 = 30.
    // Neutral: [20-30, 40-30] = [-10, 10]
    // Label NaN for data 50.0 -> result is NaN
    expected(1,0) = 10.0 - 10.0; // Group 1: data [10] (NaN is ignored for group)
    expected(1,1) = 20.0 - (20.0+40.0)/2.0;
    expected(1,2) = NaN; // Data was NaN
    expected(1,3) = 40.0 - (20.0+40.0)/2.0;
    expected(1,4) = NaN; // Label was NaN

    require_eigen_arrays_are_equal(result, expected);

    // Test case with all NaNs in a group
    DataFrame data_all_nan_group(1, 3); data_all_nan_group << NaN, NaN, 10.0;
    DataFrame labels_all_nan_group(1, 3); labels_all_nan_group << 1.0, 1.0, 2.0;
    DataFrame expected_all_nan_group(1,3); expected_all_nan_group << NaN, NaN, 0.0; // Mean of group 1 is NaN. Mean of group 2 is 10. 10-10=0.
    DataFrame result_all_nan_group = feature_operators::pn_GroupNeutral(data_all_nan_group, labels_all_nan_group);
    require_eigen_arrays_are_equal(result_all_nan_group, expected_all_nan_group);
}

TEST_CASE("GroupOps: pn_group_rank", "[GroupOps]") {
    DataFrame data(1, 6);
    data << 10.0, 20.0, 5.0, 15.0, NaN, 25.0;
    DataFrame labels(1, 6);
    labels << 1.0, 1.0, 2.0, 1.0, 2.0, 2.0;
    // Row 0:
    // Group 1 (label 1.0): data [10, 20, 15]
    //   Sorted: [10, 15, 20]. Ranks: [1,2,3]. N=3.
    //   Pct Ranks: [1/3, 2/3, 3/3] = [0.333, 0.666, 1.0]
    //   min_pr = 1/3. cut_val = (1/3)/2 = 1/6 = 0.1666...
    //   Custom Norm: [1/3-1/6, 2/3-1/6, 3/3-1/6] = [1/6, 3/6, 5/6] = [0.166..., 0.5, 0.833...]
    // Group 2 (label 2.0): data [5, NaN, 25]. Valid: [5, 25]
    //   Sorted: [5, 25]. Ranks: [1,2]. N=2.
    //   Pct Ranks: [1/2, 2/2] = [0.5, 1.0]
    //   min_pr = 0.5. cut_val = 0.5/2 = 0.25
    //   Custom Norm: [0.5-0.25, 1.0-0.25] = [0.25, 0.75]

    DataFrame result = feature_operators::pn_GroupRank(data, labels);
    DataFrame expected(1, 6);
    // Original positions:
    // Data:   10   20    5   15   NaN  25
    // Labels:  1    1    2    1   NaN   2 (label for NaN data doesn't matter, data is NaN)
    // Expected: G1 G1   G2   G1   NaN  G2
    expected(0,0) = 1.0/6.0; // 10 from G1
    expected(0,1) = 5.0/6.0; // 20 from G1
    expected(0,2) = 0.25;    // 5 from G2
    expected(0,3) = 3.0/6.0; // 15 from G1
    expected(0,4) = NaN;     // Data was NaN
    expected(0,5) = 0.75;    // 25 from G2

    require_eigen_arrays_are_equal(result, expected);
}

TEST_CASE("GroupOps: pn_group_norm", "[GroupOps]") {
    DataFrame data(1, 4);
    data << 10.0, 20.0, 5.0, 15.0; // G1: [10,20], G2: [5,15]
    DataFrame labels(1, 4);
    labels << 1.0, 1.0, 2.0, 2.0;

    // Group 1: [10, 20] -> pn_rank_custom_norm -> [0.25, 0.75] -> pn_trans_norm
    // Group 2: [5, 15]  -> pn_rank_custom_norm -> [0.25, 0.75] -> pn_trans_norm
    // Expected result for both groups (as they have 2 elements and same relative order)
    // pn_rank_custom_norm([X, Y]) where X<Y:
    //   Ranks: [1,2]. N=2. PctRanks: [0.5, 1.0]. min_pr=0.5. cut=0.25. CustomNormRanks: [0.25, 0.75]
    // Then apply boost::math::quantile(dist, clamped_value) to [0.25, 0.75]
    
    boost::math::normal dist(0.0, 1.0);
    double val1_rank_norm = 0.25; // Clamped if needed by pn_trans_norm
    double val2_rank_norm = 0.75; // Clamped if needed
    // pn_trans_norm clamps input to (epsilon, 1-epsilon)
    const double CLAMP_EPSILON_GRP = 1e-9; 
    val1_rank_norm = std::max(CLAMP_EPSILON_GRP, std::min(val1_rank_norm, 1.0 - CLAMP_EPSILON_GRP));
    val2_rank_norm = std::max(CLAMP_EPSILON_GRP, std::min(val2_rank_norm, 1.0 - CLAMP_EPSILON_GRP));

    double expected_val_g1_norm_1 = boost::math::quantile(dist, val1_rank_norm);
    double expected_val_g1_norm_2 = boost::math::quantile(dist, val2_rank_norm);
    // Same for G2 as structure is identical
    double expected_val_g2_norm_1 = boost::math::quantile(dist, val1_rank_norm);
    double expected_val_g2_norm_2 = boost::math::quantile(dist, val2_rank_norm);

    DataFrame result = feature_operators::pn_GroupNorm(data, labels);
    DataFrame expected(1, 4);
    expected << expected_val_g1_norm_1, expected_val_g1_norm_2, 
                expected_val_g2_norm_1, expected_val_g2_norm_2;
    
    require_eigen_arrays_are_equal(result, expected, 1e-7); // Quantiles can be sensitive
}
