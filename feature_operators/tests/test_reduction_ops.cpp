#include "feature_operators/reduction_ops.hpp"
#include "feature_operators/types.hpp" // Added
#include <catch2/catch_test_macros.hpp>
#include <catch2/matchers/catch_matchers_floating_point.hpp> // For Approx
#include <Eigen/Dense> // Kept
#include <limits> // For std::numeric_limits
#include <cmath>  // For std::isnan

using feature_operators::DataFrame;

const double NaN = std::numeric_limits<double>::quiet_NaN();

TEST_CASE("ReductionOps: MinVal (Array-Array)", "[ReductionOps]") {
    DataFrame a(1, 4);
    a << 1.0, NaN, 3.0, NaN;
    DataFrame b(1, 4);
    b << 2.0, 20.0, NaN, NaN;
    
    DataFrame result = feature_operators::Min(a, b);
    
    // min(1, 2) = 1
    REQUIRE_THAT(result(0,0), Catch::Matchers::WithinAbs(1.0, 1e-9));
    // min(NaN, 20) = 20
    REQUIRE_THAT(result(0,1), Catch::Matchers::WithinAbs(20.0, 1e-9));
    // min(3, NaN) = 3
    REQUIRE_THAT(result(0,2), Catch::Matchers::WithinAbs(3.0, 1e-9));
    // min(NaN, NaN) = NaN
    REQUIRE(std::isnan(result(0,3)));

    DataFrame c(1,2); c << NaN, 10.0;
    DataFrame d(1,2); d << 5.0, NaN;
    DataFrame res_cd = feature_operators::Min(c,d);
    REQUIRE_THAT(res_cd(0,0), Catch::Matchers::WithinAbs(5.0, 1e-9)); // min(NaN, 5) = 5
    REQUIRE_THAT(res_cd(0,1), Catch::Matchers::WithinAbs(10.0, 1e-9)); // min(10, NaN) = 10
}

TEST_CASE("ReductionOps: MinVal (Array-Scalar)", "[ReductionOps]") {
    DataFrame a(1, 3);
    a << 1.0, NaN, 30.0;
    double b_scalar = 10.0;
    
    DataFrame result = feature_operators::Min(a, b_scalar);
    REQUIRE_THAT(result(0,0), Catch::Matchers::WithinAbs(1.0, 1e-9));   // min(1, 10) = 1
    REQUIRE_THAT(result(0,1), Catch::Matchers::WithinAbs(10.0, 1e-9));  // min(NaN, 10) = 10
    REQUIRE_THAT(result(0,2), Catch::Matchers::WithinAbs(10.0, 1e-9));  // min(30, 10) = 10

    double nan_scalar = NaN;
    DataFrame result_nan_scalar = feature_operators::Min(a, nan_scalar);
    REQUIRE_THAT(result_nan_scalar(0,0), Catch::Matchers::WithinAbs(1.0, 1e-9));   // min(1, NaN) = 1
    REQUIRE(std::isnan(result_nan_scalar(0,1)));                                   // min(NaN, NaN) = NaN
    REQUIRE_THAT(result_nan_scalar(0,2), Catch::Matchers::WithinAbs(30.0, 1e-9));  // min(30, NaN) = 30
}

TEST_CASE("ReductionOps: MinVal (Scalar-Array)", "[ReductionOps]") {
    double a_scalar = 5.0;
    DataFrame b(1, 3);
    b << 10.0, NaN, 3.0;

    DataFrame result = feature_operators::Min(a_scalar, b);
    REQUIRE_THAT(result(0,0), Catch::Matchers::WithinAbs(5.0, 1e-9));   // min(5, 10) = 5
    REQUIRE_THAT(result(0,1), Catch::Matchers::WithinAbs(5.0, 1e-9));   // min(5, NaN) = 5
    REQUIRE_THAT(result(0,2), Catch::Matchers::WithinAbs(3.0, 1e-9));   // min(5, 3) = 3

    double nan_scalar = NaN;
    DataFrame result_nan_scalar = feature_operators::Min(nan_scalar, b);
    REQUIRE_THAT(result_nan_scalar(0,0), Catch::Matchers::WithinAbs(10.0, 1e-9));  // min(NaN, 10) = 10
    REQUIRE(std::isnan(result_nan_scalar(0,1)));                                   // min(NaN, NaN) = NaN
    REQUIRE_THAT(result_nan_scalar(0,2), Catch::Matchers::WithinAbs(3.0, 1e-9));   // min(NaN, 3) = 3
}


TEST_CASE("ReductionOps: MaxVal (Array-Array)", "[ReductionOps]") {
    DataFrame a(1, 4);
    a << 1.0, NaN, 30.0, NaN;
    DataFrame b(1, 4);
    b << 2.0, 2.0, NaN, NaN;
    
    DataFrame result = feature_operators::Max(a, b);
    
    REQUIRE_THAT(result(0,0), Catch::Matchers::WithinAbs(2.0, 1e-9));   // max(1, 2) = 2
    REQUIRE_THAT(result(0,1), Catch::Matchers::WithinAbs(2.0, 1e-9));   // max(NaN, 2) = 2
    REQUIRE_THAT(result(0,2), Catch::Matchers::WithinAbs(30.0, 1e-9));  // max(30, NaN) = 30
    REQUIRE(std::isnan(result(0,3)));                                  // max(NaN, NaN) = NaN
}

TEST_CASE("ReductionOps: MaxVal (Array-Scalar)", "[ReductionOps]") {
    DataFrame a(1, 3);
    a << 1.0, NaN, 30.0;
    double b_scalar = 10.0;
    
    DataFrame result = feature_operators::Max(a, b_scalar);
    REQUIRE_THAT(result(0,0), Catch::Matchers::WithinAbs(10.0, 1e-9)); // max(1, 10) = 10
    REQUIRE_THAT(result(0,1), Catch::Matchers::WithinAbs(10.0, 1e-9)); // max(NaN, 10) = 10
    REQUIRE_THAT(result(0,2), Catch::Matchers::WithinAbs(30.0, 1e-9)); // max(30, 10) = 30

    double nan_scalar = NaN;
    DataFrame result_nan_scalar = feature_operators::Max(a, nan_scalar);
    REQUIRE_THAT(result_nan_scalar(0,0), Catch::Matchers::WithinAbs(1.0, 1e-9));   // max(1, NaN) = 1
    REQUIRE(std::isnan(result_nan_scalar(0,1)));                                   // max(NaN, NaN) = NaN
    REQUIRE_THAT(result_nan_scalar(0,2), Catch::Matchers::WithinAbs(30.0, 1e-9));  // max(30, NaN) = 30
}

TEST_CASE("ReductionOps: MaxVal (Scalar-Array)", "[ReductionOps]") {
    double a_scalar = 5.0;
    DataFrame b(1, 3);
    b << 10.0, NaN, 3.0;

    DataFrame result = feature_operators::Max(a_scalar, b);
    REQUIRE_THAT(result(0,0), Catch::Matchers::WithinAbs(10.0, 1e-9));  // max(5, 10) = 10
    REQUIRE_THAT(result(0,1), Catch::Matchers::WithinAbs(5.0, 1e-9));    // max(5, NaN) = 5
    REQUIRE_THAT(result(0,2), Catch::Matchers::WithinAbs(5.0, 1e-9));    // max(5, 3) = 5

    double nan_scalar = NaN;
    DataFrame result_nan_scalar = feature_operators::Max(nan_scalar, b);
    REQUIRE_THAT(result_nan_scalar(0,0), Catch::Matchers::WithinAbs(10.0, 1e-9));  // max(NaN, 10) = 10
    REQUIRE(std::isnan(result_nan_scalar(0,1)));                                   // max(NaN, NaN) = NaN
    REQUIRE_THAT(result_nan_scalar(0,2), Catch::Matchers::WithinAbs(3.0, 1e-9));   // max(NaN, 3) = 3
}
