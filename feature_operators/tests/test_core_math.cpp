#include "feature_operators/core_math.hpp"
#include "feature_operators/types.hpp" // Added
#include <catch2/catch_test_macros.hpp>
#include <catch2/matchers/catch_matchers_floating_point.hpp> // For Approx
#include <Eigen/Dense> // Kept for Eigen::ArrayXXd if used locally, or can be removed if all are DataFrame
#include <limits> // For std::numeric_limits
#include <cmath>  // For std::isnan

// Using alias for convenience in test file
using feature_operators::DataFrame;

TEST_CASE("CoreMath: Add function", "[CoreMath]") {
    DataFrame a(2, 2);
    a << 1, 2, 3, 4;
    DataFrame b(2, 2);
    b << 5, 6, 7, 8;
    DataFrame expected(2, 2);
    expected << 6, 8, 10, 12;
    DataFrame result = feature_operators::Add(a, b);
    REQUIRE((result - expected).abs().maxCoeff() < 1e-9);
}

TEST_CASE("CoreMath: SqrtVal function", "[CoreMath]") {
    DataFrame a(1, 3);
    a << 4, -1, 9;
    DataFrame result = feature_operators::Sqrt(a);
    REQUIRE_THAT(result(0,0), Catch::Matchers::WithinAbs(2.0, 1e-9));
    REQUIRE(std::isnan(result(0,1)));
    REQUIRE_THAT(result(0,2), Catch::Matchers::WithinAbs(3.0, 1e-9));
}

TEST_CASE("CoreMath: PositiveLogVal function", "[CoreMath]") {
    DataFrame a(1, 4);
    a << std::exp(1.0), 1.0, 0.0, -1.0; // exp(1), 1, 0, -1
    DataFrame result = feature_operators::Log(a); // Changed from positive_log_val to Log
    REQUIRE_THAT(result(0,0), Catch::Matchers::WithinAbs(1.0, 1e-9)); // log(exp(1)) = 1
    REQUIRE_THAT(result(0,1), Catch::Matchers::WithinAbs(0.0, 1e-9)); // log(1) = 0
    REQUIRE(std::isnan(result(0,2))); // log(0) = NaN
    REQUIRE(std::isnan(result(0,3))); // log(-1) = NaN
}

TEST_CASE("CoreMath: SoftsignVal function", "[CoreMath]") {
    DataFrame a(1, 4);
    // Test cases: positive, negative, zero, and a value that would make internal sqrt negative
    a << 4.0, -9.0, 0.0, -16.0; 
    DataFrame result = feature_operators::Softsign(a);

    // Expected for a = 4.0: 4.0 / (1 + abs(sqrt(4.0))) = 4.0 / (1 + 2.0) = 4.0 / 3.0
    REQUIRE_THAT(result(0,0), Catch::Matchers::WithinAbs(4.0 / 3.0, 1e-9));
    
    // Expected for a = -9.0: -9.0 / (1 + abs(sqrt(-9.0))) = -9.0 / (1 + abs(nan)) = -9.0 / (1 + nan) = nan
    REQUIRE(std::isnan(result(0,1)));

    // Expected for a = 0.0: 0.0 / (1 + abs(sqrt(0.0))) = 0.0 / (1 + 0.0) = 0.0 / 1.0 = 0.0
    REQUIRE_THAT(result(0,2), Catch::Matchers::WithinAbs(0.0, 1e-9));

    // Expected for a = -16.0: -16.0 / (1 + abs(sqrt(-16.0))) = -16.0 / (1 + abs(nan)) = -16.0 / (1 + nan) = nan
    REQUIRE(std::isnan(result(0,3)));
}
