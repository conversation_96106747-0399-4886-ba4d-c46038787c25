#!/bin/bash

# Feature Operators Benchmark Test Runner
# 性能测试运行脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示帮助信息
show_help() {
    echo "Feature Operators Benchmark Test Runner"
    echo "======================================="
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --help                    显示此帮助信息"
    echo "  -c, --category CATEGORY       指定测试分类"
    echo "  -l, --language LANG          指定语言 (python|cpp)"
    echo "  -i, --iterations NUM         指定迭代次数 (默认: 100)"
    echo "  --build                      构建C++测试程序"
    echo "  --clean                      清理构建文件"
    echo "  --compare                    运行Python vs C++性能比较"
    echo "  --compare-v2                 运行C++ V1 vs V2性能比较"
    echo "  --run-all                    运行完整性能测试流程"
    echo ""
    echo "分类 (CATEGORY):"
    echo "  core_math        基础数学运算"
    echo "  logical_ops      逻辑运算"
    echo "  comparison_ops   比较运算"
    echo "  data_utils       数据工具"
    echo "  reduction_ops    归约运算"
    echo "  timeseries_ops   时间序列运算"
    echo "  panel_ops        面板运算"
    echo "  group_ops        分组运算"
    echo "  all              所有分类"
    echo ""
    echo "语言 (LANG):"
    echo "  python           Python实现"
    echo "  cpp              C++实现"
    echo ""
    echo "示例:"
    echo "  $0 --build                                    # 构建C++测试程序"
    echo "  $0 -c core_math -l python -i 200             # 运行Python性能测试"
    echo "  $0 -c timeseries_ops -l cpp -i 100           # 运行C++性能测试"
    echo "  $0 --compare -c all                          # 运行Python vs C++性能比较"
    echo "  $0 --compare-v2                              # 运行C++ V1 vs V2性能比较"
    echo "  $0 --run-all -c all -i 100                   # 运行完整性能测试流程"
}

# 构建测试程序
build_tests() {
    print_info "构建性能测试程序..."

    if [ ! -d "build" ]; then
        mkdir build
    fi

    cd build
    cmake ..
    make unified_benchmark -j$(nproc)
    cd ..

    print_success "性能测试程序构建完成"
}

# 清理构建文件
clean_build() {
    print_info "清理构建文件..."

    if [ -d "build" ]; then
        rm -rf build
        print_success "构建文件已清理"
    else
        print_warning "没有找到构建文件"
    fi
}

# 运行Python性能测试
run_python_benchmark() {
    local category=$1
    local iterations=$2

    print_info "运行Python性能测试: $category (迭代次数: $iterations)"

    python3 test_benchmark/unified_benchmark_test.py --category "$category" --iterations "$iterations"
    local exit_code=$?

    if [ $exit_code -eq 0 ]; then
        print_success "Python性能测试完成: $category"
        return 0
    else
        print_warning "Python性能测试部分失败: $category"
        print_warning "部分算子可能执行失败，但已生成的结果仍可用于后续比较"
        return 1
    fi
}

# 运行C++性能测试
run_cpp_benchmark() {
    local category=$1
    local iterations=$2

    print_info "运行C++性能测试: $category (迭代次数: $iterations)"

    if [ ! -f "build/unified_benchmark" ]; then
        print_error "未找到性能测试程序，请先运行 --build"
        return 1
    fi

    cd build
    ./unified_benchmark "$category" "$iterations"
    local result=$?
    cd ..

    if [ $result -eq 0 ]; then
        print_success "C++性能测试完成: $category"
        return 0
    else
        print_warning "C++性能测试部分失败: $category"
        print_warning "部分算子可能执行失败，但已生成的结果仍可用于后续比较"
        return 1
    fi
}

# 运行Python vs C++性能比较
run_performance_comparison() {
    local category=$1

    print_info "运行Python vs C++性能比较: $category"

    python3 test_benchmark/compare_python_cpp_performance.py --category "$category"

    if [ $? -eq 0 ]; then
        print_success "性能比较完成: $category"
        return 0
    else
        print_error "性能比较失败: $category"
        return 1
    fi
}

# 运行C++ V1 vs V2性能比较
run_v2_performance_comparison() {
    print_info "运行C++ V1 vs V2性能比较"

    python3 test_benchmark/compare_cpp_v1_v2_performance.py

    if [ $? -eq 0 ]; then
        print_success "V1 vs V2性能比较完成"
        return 0
    else
        print_error "V1 vs V2性能比较失败"
        return 1
    fi
}

# 运行完整性能测试流程
run_all_benchmark() {
    local category=$1
    local iterations=$2

    print_info "开始运行完整性能测试流程: $category (迭代次数: $iterations)"

    # 步骤1: 构建程序
    print_info "步骤 1/5: 构建C++测试程序"
    if ! build_tests; then
        print_error "构建失败，终止流程"
        return 1
    fi

    # 步骤2: 运行Python性能测试
    print_info "步骤 2/5: 运行Python性能测试"
    if ! run_python_benchmark "$category" "$iterations"; then
        print_warning "Python性能测试部分失败，但继续执行后续步骤"
        print_warning "请检查Python性能测试输出以了解具体失败的算子"
    fi

    # 步骤3: 运行C++性能测试
    print_info "步骤 3/5: 运行C++性能测试"
    if ! run_cpp_benchmark "$category" "$iterations"; then
        print_warning "C++性能测试部分失败，但继续执行后续步骤"
        print_warning "请检查C++性能测试输出以了解具体失败的算子"
    fi

    # 步骤4: Python vs C++性能比较
    print_info "步骤 4/5: 运行Python vs C++性能比较"
    if ! run_performance_comparison "$category"; then
        print_warning "Python vs C++性能比较失败，但继续执行后续步骤"
    fi

    # 步骤5: V1 vs V2性能比较
    print_info "步骤 5/5: 运行C++ V1 vs V2性能比较"
    if ! run_v2_performance_comparison; then
        print_warning "V1 vs V2性能比较失败"
    fi

    print_success "完整性能测试流程执行完成!"
    print_info "请查看以下目录的结果:"
    print_info "  - Python性能结果: test_benchmark/results/python/"
    print_info "  - C++性能结果: test_benchmark/results/cpp/"
    print_info "  - 性能比较结果: test_benchmark/results/comparison/"

    return 0
}

# 主函数
main() {
    local category=""
    local language=""
    local iterations=30
    local action=""

    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -c|--category)
                category="$2"
                shift 2
                ;;
            -l|--language)
                language="$2"
                shift 2
                ;;
            -i|--iterations)
                iterations="$2"
                shift 2
                ;;
            --build)
                action="build"
                shift
                ;;
            --clean)
                action="clean"
                shift
                ;;
            --compare)
                action="compare"
                shift
                ;;
            --compare-v2)
                action="compare_v2"
                shift
                ;;
            --run-all)
                action="run_all"
                shift
                ;;
            *)
                print_error "未知选项: $1"
                show_help
                exit 1
                ;;
        esac
    done

    # 执行相应的操作
    case $action in
        build)
            build_tests
            ;;
        clean)
            clean_build
            ;;
        compare)
            if [ -z "$category" ]; then
                category="all"
            fi
            run_performance_comparison "$category"
            ;;
        compare_v2)
            run_v2_performance_comparison
            ;;
        run_all)
            if [ -z "$category" ]; then
                category="all"
            fi
            run_all_benchmark "$category" "$iterations"
            ;;
        "")
            # 没有指定action，根据参数执行测试
            if [ -z "$category" ] || [ -z "$language" ]; then
                print_error "请指定分类和语言，或使用预定义的操作"
                show_help
                exit 1
            fi

            if [ "$language" = "python" ]; then
                run_python_benchmark "$category" "$iterations"
            elif [ "$language" = "cpp" ]; then
                run_cpp_benchmark "$category" "$iterations"
            else
                print_error "未知语言: $language"
                exit 1
            fi
            ;;
        *)
            print_error "未知操作: $action"
            exit 1
            ;;
    esac
}

# 检查是否在正确的目录中
if [ ! -f "test_benchmark/unified_benchmark_test.py" ]; then
    print_error "请在 feature_operators/tests 目录中运行此脚本"
    exit 1
fi

# 运行主函数
main "$@"
