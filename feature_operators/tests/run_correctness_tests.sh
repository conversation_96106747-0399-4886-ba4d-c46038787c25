#!/bin/bash

# Feature Operators Correctness Test Runner
# 正确性测试运行脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示帮助信息
show_help() {
    echo "Feature Operators Correctness Test Runner"
    echo "========================================"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --help                    显示此帮助信息"
    echo "  -c, --category CATEGORY       指定测试分类"
    echo "  -l, --language LANG          指定语言 (python|cpp)"
    echo "  -t, --tolerance NUM          指定比较容差 (默认: 1e-8)"
    echo "  --build                      构建C++测试程序"
    echo "  --clean                      清理构建文件"
    echo "  --compare                    运行比较分析"
    echo "  --compare-v2                 运行V2版本比较"
    echo "  --analyze                    分析测试结果"
    echo "  --report                     生成测试报告"
    echo "  --run-all                    运行完整测试流程"
    echo ""
    echo "分类 (CATEGORY):"
    echo "  core_math        基础数学运算"
    echo "  logical_ops      逻辑运算"
    echo "  comparison_ops   比较运算"
    echo "  data_utils       数据工具"
    echo "  reduction_ops    归约运算"
    echo "  timeseries_ops   时间序列运算"
    echo "  panel_ops        面板运算"
    echo "  group_ops        分组运算"
    echo "  all              所有分类"
    echo ""
    echo "语言 (LANG):"
    echo "  python           Python实现"
    echo "  cpp              C++实现"
    echo ""
    echo "示例:"
    echo "  $0 --build                                    # 构建C++测试程序"
    echo "  $0 -c core_math -l python                    # 运行Python正确性测试"
    echo "  $0 -c timeseries_ops -l cpp                  # 运行C++正确性测试"
    echo "  $0 --compare -c all                          # 运行Python vs C++比较"
    echo "  $0 --compare-v2                              # 运行V2版本比较"
    echo "  $0 --analyze -c all                          # 分析测试结果"
    echo "  $0 --report -c core_math                     # 生成详细报告"
    echo "  $0 --run-all -c all                          # 运行完整测试流程"
}

# 构建测试程序
build_tests() {
    print_info "构建正确性测试程序..."

    if [ ! -d "build" ]; then
        mkdir build
    fi

    cd build
    cmake ..
    make unified_correctness_test -j$(nproc)
    cd ..

    print_success "正确性测试程序构建完成"
}

# 清理构建文件
clean_build() {
    print_info "清理构建文件..."

    if [ -d "build" ]; then
        rm -rf build
        print_success "构建文件已清理"
    else
        print_warning "没有找到构建文件"
    fi
}

# 运行Python正确性测试
run_python_correctness() {
    local category=$1

    print_info "运行Python正确性测试: $category"

    python3 test_right/unified_correctness_test.py --category "$category"
    local exit_code=$?

    if [ $exit_code -eq 0 ]; then
        print_success "Python正确性测试完成: $category"
        return 0
    else
        print_warning "Python正确性测试部分失败: $category"
        print_warning "部分算子可能执行失败，但已生成的结果仍可用于后续比较"
        return 1
    fi
}

# 运行C++正确性测试
run_cpp_correctness() {
    local category=$1

    print_info "运行C++正确性测试: $category"

    if [ ! -f "build/unified_correctness_test" ]; then
        print_error "未找到正确性测试程序，请先运行 --build"
        return 1
    fi

    cd build
    ./unified_correctness_test "$category"
    local result=$?
    cd ..

    if [ $result -eq 0 ]; then
        print_success "C++正确性测试完成: $category"
        return 0
    else
        print_warning "C++正确性测试部分失败: $category"
        print_warning "部分算子可能执行失败，但已生成的结果仍可用于后续比较"
        return 1
    fi
}

# 运行比较分析
run_comparison() {
    local category=$1
    local tolerance=$2

    print_info "运行Python vs C++比较分析: $category"

    python3 test_right/simple_compare.py --mode python_vs_cpp --category "$category" --tolerance "$tolerance"

    if [ $? -eq 0 ]; then
        print_success "比较分析完成: $category"
        return 0
    else
        print_error "比较分析失败: $category"
        return 1
    fi
}

# 运行V2版本比较
run_v2_comparison() {
    local tolerance=$1

    print_info "运行V2版本比较分析 (C++ V2 vs Python)"

    python3 test_right/compare_v1_v2_operators.py --tolerance "$tolerance"

    if [ $? -eq 0 ]; then
        print_success "V2版本比较完成"
        return 0
    else
        print_error "V2版本比较失败"
        return 1
    fi
}

# 运行结果分析
run_analysis() {
    local category=$1

    print_info "运行结果分析: $category"

    # 使用简单的内置分析
    analyze_comparison_results "$category"
    local result=$?

    if [ $result -eq 0 ]; then
        print_success "结果分析完成: $category"
        return 0
    else
        print_warning "结果分析失败，但不影响整体流程"
        return 0  # 返回成功，不让分析失败影响整体流程
    fi
}

# 简单的结果分析函数
analyze_comparison_results() {
    local category=$1
    local comparison_dir="test_right/results/comparison"

    print_info "分析比较结果: $category"

    # 检查比较结果文件
    local json_files=$(find "$comparison_dir" -name "*.json" 2>/dev/null)

    if [ -z "$json_files" ]; then
        print_warning "没有找到比较结果文件进行分析"
        return 1
    fi

    print_info "找到以下比较结果文件:"
    echo "$json_files" | while read -r file; do
        if [ -n "$file" ]; then
            print_info "  - $(basename "$file")"
        fi
    done

    print_success "结果分析完成，文件可用于后续处理"
    return 0
}

# 生成测试报告
run_report() {
    local category=$1

    print_info "生成测试报告: $category"

    # 使用内置的简单报告生成
    generate_simple_report "$category"
    local result=$?

    if [ $result -eq 0 ]; then
        print_success "测试报告生成完成: $category"
        return 0
    else
        print_warning "测试报告生成失败，但不影响整体流程"
        return 0  # 返回成功，不让报告生成失败影响整体流程
    fi
}

# 简化的报告生成函数（Python脚本会自动生成详细报告）
generate_simple_report() {
    local category=$1
    local reports_dir="test_right/results/reports"

    print_info "检查Python脚本生成的详细报告"

    # 检查reports目录中是否有报告文件
    local report_count=$(find "$reports_dir" -name "*detailed_report*.txt" 2>/dev/null | wc -l)

    if [ "$report_count" -gt 0 ]; then
        print_success "找到 $report_count 个详细报告文件:"
        find "$reports_dir" -name "*detailed_report*.txt" 2>/dev/null | while read -r file; do
            print_info "  - $(basename "$file")"
        done
        return 0
    else:
        print_warning "没有找到详细报告文件，但Python脚本应该已经生成了"
        return 0  # 不影响整体流程
    fi
}

# 运行完整测试流程
run_all_correctness() {
    local category=$1
    local tolerance=$2

    print_info "开始运行完整正确性测试流程: $category"

    # 步骤1: 构建程序
    print_info "步骤 1/7: 构建C++测试程序"
    if ! build_tests; then
        print_error "构建失败，终止流程"
        return 1
    fi

    # 步骤2: 运行Python测试
    print_info "步骤 2/7: 运行Python正确性测试"
    if ! run_python_correctness "$category"; then
        print_warning "Python测试部分失败，但继续执行后续步骤"
        print_warning "请检查Python测试输出以了解具体失败的算子"
    fi

    # 步骤3: 运行C++测试
    print_info "步骤 3/7: 运行C++正确性测试"
    if ! run_cpp_correctness "$category"; then
        print_warning "C++测试部分失败，但继续执行后续步骤"
        print_warning "请检查C++测试输出以了解具体失败的算子"
    fi

    # 步骤4: Python vs C++比较
    print_info "步骤 4/7: 运行Python vs C++比较"
    if ! run_comparison "$category" "$tolerance"; then
        print_warning "Python vs C++比较失败，但继续执行后续步骤"
    fi

    # 步骤5: V2版本比较
    print_info "步骤 5/7: 运行V2版本比较"
    if ! run_v2_comparison "$tolerance"; then
        print_warning "V2版本比较失败，但继续执行后续步骤"
    fi

    # 步骤6: 分析结果
    print_info "步骤 6/7: 分析测试结果"
    if ! run_analysis "$category"; then
        print_warning "结果分析失败，但继续执行后续步骤"
    fi

    # 步骤7: 生成报告
    print_info "步骤 7/7: 生成测试报告"
    if ! run_report "$category"; then
        print_warning "报告生成失败"
    fi

    print_success "完整正确性测试流程执行完成!"
    print_info ""
    print_info "测试结果总结:"
    print_info "=============="
    print_info "✓ 所有步骤已执行完成"
    print_info "✓ 即使部分算子失败，流程仍继续执行"
    print_info "✓ 成功生成的结果可用于后续分析"
    print_info ""
    print_info "请查看以下目录的结果:"
    print_info "  - Python结果: test_right/results/python/"
    print_info "  - C++结果: test_right/results/cpp/"
    print_info "  - 比较结果: test_right/results/comparison/"
    print_info "  - 分析报告: test_right/results/reports/"
    print_info ""
    print_info "如果有算子失败，请检查上述输出中的错误信息"
    print_info "失败的算子不会影响其他算子的测试和比较"

    return 0
}

# 主函数
main() {
    local category=""
    local language=""
    local tolerance=1e-8
    local action=""

    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -c|--category)
                category="$2"
                shift 2
                ;;
            -l|--language)
                language="$2"
                shift 2
                ;;
            -t|--tolerance)
                tolerance="$2"
                shift 2
                ;;
            --build)
                action="build"
                shift
                ;;
            --clean)
                action="clean"
                shift
                ;;
            --compare)
                action="compare"
                shift
                ;;
            --compare-v2)
                action="compare_v2"
                shift
                ;;
            --analyze)
                action="analyze"
                shift
                ;;
            --report)
                action="report"
                shift
                ;;
            --run-all)
                action="run_all"
                shift
                ;;
            *)
                print_error "未知选项: $1"
                show_help
                exit 1
                ;;
        esac
    done

    # 执行相应的操作
    case $action in
        build)
            build_tests
            ;;
        clean)
            clean_build
            ;;
        compare)
            if [ -z "$category" ]; then
                category="all"
            fi
            run_comparison "$category" "$tolerance"
            ;;
        compare_v2)
            run_v2_comparison "$tolerance"
            ;;
        analyze)
            if [ -z "$category" ]; then
                category="all"
            fi
            run_analysis "$category"
            ;;
        report)
            if [ -z "$category" ]; then
                category="all"
            fi
            run_report "$category"
            ;;
        run_all)
            if [ -z "$category" ]; then
                category="all"
            fi
            run_all_correctness "$category" "$tolerance"
            ;;
        "")
            # 没有指定action，根据参数执行测试
            if [ -z "$category" ] || [ -z "$language" ]; then
                print_error "请指定分类和语言，或使用预定义的操作"
                show_help
                exit 1
            fi

            if [ "$language" = "python" ]; then
                run_python_correctness "$category"
            elif [ "$language" = "cpp" ]; then
                run_cpp_correctness "$category"
            else
                print_error "未知语言: $language"
                exit 1
            fi
            ;;
        *)
            print_error "未知操作: $action"
            exit 1
            ;;
    esac
}

# 检查是否在正确的目录中
if [ ! -f "test_right/unified_correctness_test.py" ]; then
    print_error "请在 feature_operators/tests 目录中运行此脚本"
    exit 1
fi

# 运行主函数
main "$@"
