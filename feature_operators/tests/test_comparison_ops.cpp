#include "feature_operators/comparison_ops.hpp"
#include "feature_operators/types.hpp" // Added
#include <catch2/catch_test_macros.hpp>
#include <catch2/matchers/catch_matchers_floating_point.hpp> // For Approx
#include <Eigen/Dense> // Kept
#include <limits> // For std::numeric_limits
#include <cmath>  // For std::isnan

using feature_operators::DataFrame;

const double NaN = std::numeric_limits<double>::quiet_NaN();

TEST_CASE("ComparisonOps: GreaterThanVal (Array version)", "[ComparisonOps]") {
    DataFrame a(2, 2);
    a << 1.0, 5.0, NaN, 2.0;
    DataFrame b(2, 2);
    b << 0.0, 5.0, 10.0, NaN;
    
    DataFrame result = feature_operators::Mthan(a, b);
    
    // a(0,0)=1, b(0,0)=0 => 1 > 0 => 1.0
    REQUIRE_THAT(result(0,0), Catch::Matchers::WithinAbs(1.0, 1e-9));
    // a(0,1)=5, b(0,1)=5 => 5 > 5 => False => 0.0
    REQUIRE_THAT(result(0,1), Catch::Matchers::WithinAbs(0.0, 1e-9));
    // a(1,0)=NaN, b(1,0)=10 => NaN > 10 => False => 0.0
    REQUIRE_THAT(result(1,0), Catch::Matchers::WithinAbs(0.0, 1e-9));
    // a(1,1)=2, b(1,1)=NaN => 2 > NaN => False => 0.0
    REQUIRE_THAT(result(1,1), Catch::Matchers::WithinAbs(0.0, 1e-9));
}

TEST_CASE("ComparisonOps: GreaterEqualVal (Array version)", "[ComparisonOps]") {
    DataFrame a(1, 3); a << 5.0, NaN, 3.0;
    DataFrame b(1, 3); b << 5.0, 2.0, 4.0;
    DataFrame res = feature_operators::MEthan(a,b);
    REQUIRE_THAT(res(0,0), Catch::Matchers::WithinAbs(1.0, 1e-9)); // 5 >= 5 -> T
    REQUIRE_THAT(res(0,1), Catch::Matchers::WithinAbs(0.0, 1e-9)); // NaN >= 2 -> F
    REQUIRE_THAT(res(0,2), Catch::Matchers::WithinAbs(0.0, 1e-9)); // 3 >= 4 -> F
}


TEST_CASE("ComparisonOps: LessThanVal (Array version)", "[ComparisonOps]") {
    DataFrame a(1, 3); a << 5.0, NaN, 3.0;
    DataFrame b(1, 3); b << 5.0, 2.0, 2.0;
    DataFrame res = feature_operators::Lthan(a,b);
    REQUIRE_THAT(res(0,0), Catch::Matchers::WithinAbs(0.0, 1e-9)); // 5 < 5 -> F
    REQUIRE_THAT(res(0,1), Catch::Matchers::WithinAbs(0.0, 1e-9)); // NaN < 2 -> F
    REQUIRE_THAT(res(0,2), Catch::Matchers::WithinAbs(0.0, 1e-9)); // 3 < 2 -> F
}

TEST_CASE("ComparisonOps: LessEqualVal (Array version)", "[ComparisonOps]") {
    DataFrame a(1, 3); a << 5.0, NaN, 3.0;
    DataFrame b(1, 3); b << 5.0, 2.0, 4.0;
    DataFrame res = feature_operators::LEthan(a,b);
    REQUIRE_THAT(res(0,0), Catch::Matchers::WithinAbs(1.0, 1e-9)); // 5 <= 5 -> T
    REQUIRE_THAT(res(0,1), Catch::Matchers::WithinAbs(0.0, 1e-9)); // NaN <= 2 -> F
    REQUIRE_THAT(res(0,2), Catch::Matchers::WithinAbs(1.0, 1e-9)); // 3 <= 4 -> T
}

TEST_CASE("ComparisonOps: EqualVal (Scalar version)", "[ComparisonOps]") {
    DataFrame a(2, 2);
    a << 10.0, NaN, 20.0, 10.0;
    double b_scalar = 10.0;
    
    DataFrame result = feature_operators::Equal(a, b_scalar);
    
    // a(0,0)=10, b=10 => 10 == 10 => 1.0
    REQUIRE_THAT(result(0,0), Catch::Matchers::WithinAbs(1.0, 1e-9));
    // a(0,1)=NaN, b=10 => NaN == 10 => False => 0.0
    REQUIRE_THAT(result(0,1), Catch::Matchers::WithinAbs(0.0, 1e-9));
    // a(1,0)=20, b=10 => 20 == 10 => False => 0.0
    REQUIRE_THAT(result(1,0), Catch::Matchers::WithinAbs(0.0, 1e-9));
    // a(1,1)=10, b=10 => 10 == 10 => 1.0
    REQUIRE_THAT(result(1,1), Catch::Matchers::WithinAbs(1.0, 1e-9));

    DataFrame c(1,1); c << NaN;
    REQUIRE_THAT(feature_operators::Equal(c, NaN)(0,0), Catch::Matchers::WithinAbs(0.0, 1e-9)); // NaN == NaN -> F
}

TEST_CASE("ComparisonOps: UnequalVal (Scalar version)", "[ComparisonOps]") {
    DataFrame a(1, 3); a << 5.0, NaN, 3.0;
    double b_scalar = 5.0;
    DataFrame res = feature_operators::UnEqual(a, b_scalar);
    REQUIRE_THAT(res(0,0), Catch::Matchers::WithinAbs(0.0, 1e-9)); // 5 != 5 -> F
    REQUIRE_THAT(res(0,1), Catch::Matchers::WithinAbs(1.0, 1e-9)); // NaN != 5 -> T (Eigen specific, standard C++ std::isnan(NaN) != 5 is also true)
                                                                    // Python: np.nan != 5 is True
    REQUIRE_THAT(res(0,2), Catch::Matchers::WithinAbs(1.0, 1e-9)); // 3 != 5 -> T

    DataFrame c(1,1); c << NaN;
    REQUIRE_THAT(feature_operators::UnEqual(c, NaN)(0,0), Catch::Matchers::WithinAbs(1.0, 1e-9)); // NaN != NaN -> T
}
