#include "feature_operators/logical_ops.hpp"
#include "feature_operators/types.hpp" // Added
#include <catch2/catch_test_macros.hpp>
#include <catch2/matchers/catch_matchers_floating_point.hpp> // For Approx
#include <Eigen/Dense> // Kept
#include <limits> // For std::numeric_limits
#include <cmath>  // For std::isnan

using feature_operators::DataFrame;

const double NaN = std::numeric_limits<double>::quiet_NaN();

TEST_CASE("LogicalOps: IfThenElseVal (Array version)", "[LogicalOps]") {
    DataFrame cond(2, 2);
    cond << 1.0, -1.0, 0.0, NaN;
    
    DataFrame then_val(2, 2);
    then_val << 10.0, 20.0, 30.0, 40.0;
    
    DataFrame else_val(2, 2);
    else_val << 100.0, 200.0, 300.0, 400.0;
    
    DataFrame result = feature_operators::IfThen(cond, then_val, else_val);
    
    REQUIRE_THAT(result(0,0), Catch::Matchers::WithinAbs(10.0, 1e-9)); // cond > 0
    REQUIRE_THAT(result(0,1), Catch::Matchers::WithinAbs(200.0, 1e-9)); // cond < 0
    REQUIRE(std::isnan(result(1,0))); // cond == 0
    REQUIRE(std::isnan(result(1,1))); // cond is NaN
}

TEST_CASE("LogicalOps: IfThenElseVal (Scalar then_val version)", "[LogicalOps]") {
    DataFrame cond(1, 3);
    cond << 5.0, -5.0, NaN;
    double then_scalar = 55.0;
    DataFrame else_val(1, 3);
    else_val << 1.0, 2.0, 3.0;

    DataFrame result = feature_operators::IfThen(cond, then_scalar, else_val);
    REQUIRE_THAT(result(0,0), Catch::Matchers::WithinAbs(55.0, 1e-9));
    REQUIRE_THAT(result(0,1), Catch::Matchers::WithinAbs(2.0, 1e-9));
    REQUIRE(std::isnan(result(0,2)));
}

TEST_CASE("LogicalOps: IfThenElseVal (Scalar else_val version)", "[LogicalOps]") {
    DataFrame cond(1, 3);
    cond << 5.0, -5.0, 0.0;
    DataFrame then_val(1, 3);
    then_val << 1.0, 2.0, 3.0;
    double else_scalar = 77.0;

    DataFrame result = feature_operators::IfThen(cond, then_val, else_scalar);
    REQUIRE_THAT(result(0,0), Catch::Matchers::WithinAbs(1.0, 1e-9));
    REQUIRE_THAT(result(0,1), Catch::Matchers::WithinAbs(77.0, 1e-9));
    REQUIRE(std::isnan(result(0,2)));
}

TEST_CASE("LogicalOps: IfThenElseVal (Scalar then_val and else_val version)", "[LogicalOps]") {
    DataFrame cond(1, 3);
    cond << 5.0, -5.0, 0.0;
    double then_scalar = 55.0;
    double else_scalar = 77.0;

    DataFrame result = feature_operators::IfThen(cond, then_scalar, else_scalar);
    REQUIRE_THAT(result(0,0), Catch::Matchers::WithinAbs(55.0, 1e-9));
    REQUIRE_THAT(result(0,1), Catch::Matchers::WithinAbs(77.0, 1e-9));
    REQUIRE(std::isnan(result(0,2)));
}


TEST_CASE("LogicalOps: AndVal (Array version)", "[LogicalOps]") {
    DataFrame a(2, 2);
    // Python: (a != 0 and b != 0) if not (a.isnan() or b.isnan()) else False (0.0)
    a << 1.0, 0.0, 5.0, NaN; 
    DataFrame b(2, 2);
    b << 1.0, 5.0, NaN, 5.0;
    
    DataFrame result = feature_operators::And(a, b);
    
    // a(0,0)=1, b(0,0)=1 => !isnan(a) && !isnan(b) => (1!=0 && 1!=0) => 1.0
    REQUIRE_THAT(result(0,0), Catch::Matchers::WithinAbs(1.0, 1e-9));
    // a(0,1)=0, b(0,1)=5 => !isnan(a) && !isnan(b) => (0!=0 && 5!=0) => False => 0.0
    REQUIRE_THAT(result(0,1), Catch::Matchers::WithinAbs(0.0, 1e-9));
    // a(1,0)=5, b(1,0)=NaN => isnan(b) => 0.0
    REQUIRE_THAT(result(1,0), Catch::Matchers::WithinAbs(0.0, 1e-9));
    // a(1,1)=NaN, b(1,1)=5 => isnan(a) => 0.0
    REQUIRE_THAT(result(1,1), Catch::Matchers::WithinAbs(0.0, 1e-9));
}

TEST_CASE("LogicalOps: AndVal (Scalar version)", "[LogicalOps]") {
    DataFrame a(1, 3);
    a << 1.0, 0.0, NaN;
    double b_scalar_true = 1.0; // true
    double b_scalar_false = 0.0; // false

    DataFrame result_true_b = feature_operators::And(a, b_scalar_true);
    REQUIRE_THAT(result_true_b(0,0), Catch::Matchers::WithinAbs(1.0, 1e-9)); // 1 && 1 -> 1
    REQUIRE_THAT(result_true_b(0,1), Catch::Matchers::WithinAbs(0.0, 1e-9)); // 0 && 1 -> 0
    REQUIRE_THAT(result_true_b(0,2), Catch::Matchers::WithinAbs(0.0, 1e-9)); // NaN && 1 -> 0

    DataFrame result_false_b = feature_operators::And(a, b_scalar_false);
    REQUIRE_THAT(result_false_b(0,0), Catch::Matchers::WithinAbs(0.0, 1e-9)); // 1 && 0 -> 0
    REQUIRE_THAT(result_false_b(0,1), Catch::Matchers::WithinAbs(0.0, 1e-9)); // 0 && 0 -> 0
    REQUIRE_THAT(result_false_b(0,2), Catch::Matchers::WithinAbs(0.0, 1e-9)); // NaN && 0 -> 0
}

TEST_CASE("LogicalOps: OrVal (Array version)", "[LogicalOps]") {
    DataFrame a(2, 2);
    a << 1.0, 0.0, 0.0, NaN;
    DataFrame b(2, 2);
    b << 0.0, 1.0, NaN, 0.0;

    DataFrame result = feature_operators::Or(a, b);
    // a(0,0)=1, b(0,0)=0 => (1!=0 || 0!=0) => True => 1.0
    REQUIRE_THAT(result(0,0), Catch::Matchers::WithinAbs(1.0, 1e-9));
    // a(0,1)=0, b(0,1)=1 => (0!=0 || 1!=0) => True => 1.0
    REQUIRE_THAT(result(0,1), Catch::Matchers::WithinAbs(1.0, 1e-9));
    // a(1,0)=0, b(1,0)=NaN => isnan(b) => 0.0
    REQUIRE_THAT(result(1,0), Catch::Matchers::WithinAbs(0.0, 1e-9));
    // a(1,1)=NaN, b(1,1)=0 => isnan(a) => 0.0
    REQUIRE_THAT(result(1,1), Catch::Matchers::WithinAbs(0.0, 1e-9));

    DataFrame c(1,2); c << 0.0, NaN;
    DataFrame d(1,2); d << 0.0, NaN;
    DataFrame res_cd = feature_operators::Or(c,d);
    REQUIRE_THAT(res_cd(0,0), Catch::Matchers::WithinAbs(0.0, 1e-9)); // 0 || 0 -> 0
    REQUIRE_THAT(res_cd(0,1), Catch::Matchers::WithinAbs(0.0, 1e-9)); // NaN || NaN -> 0
}

TEST_CASE("LogicalOps: NotVal", "[LogicalOps]") {
    DataFrame a(1, 3);
    a << 1.0, 0.0, NaN; // True, False, NaN
    DataFrame result = feature_operators::Not(a);
    // a(0,0)=1 => (1==0) => False => 0.0
    REQUIRE_THAT(result(0,0), Catch::Matchers::WithinAbs(0.0, 1e-9));
    // a(0,1)=0 => (0==0) => True => 1.0
    REQUIRE_THAT(result(0,1), Catch::Matchers::WithinAbs(1.0, 1e-9));
    // a(0,2)=NaN => isnan(a) => 0.0
    REQUIRE_THAT(result(0,2), Catch::Matchers::WithinAbs(0.0, 1e-9));
}

TEST_CASE("LogicalOps: XorVal (Array version)", "[LogicalOps]") {
    DataFrame a(2, 2);
    a << 1.0, 0.0, 1.0, NaN;
    DataFrame b(2, 2);
    b << 0.0, 1.0, 1.0, 0.0;
    
    DataFrame result = feature_operators::Xor(a, b);
    // a=1, b=0 => (1!=0 && 0==0) || (1==0 && 0!=0) => True => 1.0
    REQUIRE_THAT(result(0,0), Catch::Matchers::WithinAbs(1.0, 1e-9));
    // a=0, b=1 => (0!=0 && 1==0) || (0==0 && 1!=0) => True => 1.0
    REQUIRE_THAT(result(0,1), Catch::Matchers::WithinAbs(1.0, 1e-9));
    // a=1, b=1 => (1!=0 && 1==0) || (1==0 && 1!=0) => False => 0.0
    REQUIRE_THAT(result(1,0), Catch::Matchers::WithinAbs(0.0, 1e-9));
    // a=NaN, b=0 => isnan(a) => 0.0
    REQUIRE_THAT(result(1,1), Catch::Matchers::WithinAbs(0.0, 1e-9));

    DataFrame c(1,1); c << NaN;
    DataFrame d(1,1); d << NaN;
    REQUIRE_THAT(feature_operators::Xor(c,d)(0,0), Catch::Matchers::WithinAbs(0.0, 1e-9));
}
