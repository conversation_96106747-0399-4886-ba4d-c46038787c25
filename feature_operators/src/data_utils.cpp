// #include "feature_operators/data_utils.hpp"
// #include "feature_operators/types.hpp" // Added
// #include "feature_operators/timeseries_ops.hpp"
// #include <Eigen/Dense>                 // Kept for other Eigen types
// #include <cassert>                     // For assert
// #include <limits>                      // For std::numeric_limits
// #include <cmath>  // For std::isnan, std::isinf, std::floor
// #include <limits> // For std::numeric_limits
// namespace feature_operators {

// DataFrame FilterInf(const DataFrame &a) {
//   return a.unaryExpr([](double x) {
//     if (std::isinf(x)) {
//       return std::numeric_limits<double>::quiet_NaN();
//     }
//     return x;
//   });
// }

// DataFrame FillNan(const DataFrame &data, double fill_value) {
//   return (data.isNaN()).select(fill_value, data);
// }

// DataFrame FillNan(const DataFrame &data, const DataFrame &fill_data) {
//   assert(data.rows() == fill_data.rows() && data.cols() == fill_data.cols());
//   return (data.isNaN()).select(fill_data, data);
// }

// DataFrame getNan(const DataFrame &a) {
//   return DataFrame::Constant(a.rows(), a.cols(),
//                              std::numeric_limits<double>::quiet_NaN());
// }

// DataFrame getInf(const DataFrame &a) {
//   return DataFrame::Constant(a.rows(), a.cols(),
//                              std::numeric_limits<double>::infinity());
// }

// DataFrame GetSingleBar(const DataFrame &aa, const DataFrame &idxx) {
//   assert(aa.rows() == idxx.rows() &&
//          "GetSingleBar: aa and idxx must have the same number of rows.");
//   assert(aa.cols() == idxx.cols() &&
//          "GetSingleBar: aa and idxx must have the same number of columns.");

//   const double NaN = std::numeric_limits<double>::quiet_NaN();
//   DataFrame out = DataFrame::Constant(aa.rows(), aa.cols(), NaN);

//   // Determine max_iter_val
//   double max_val_in_idxx = -std::numeric_limits<double>::infinity();
//   bool found_finite_in_idxx = false;
//   for (Eigen::Index r = 0; r < idxx.rows(); ++r) {
//     for (Eigen::Index c = 0; c < idxx.cols(); ++c) {
//       if (std::isfinite(idxx(r, c))) {
//         if (idxx(r, c) > max_val_in_idxx) {
//           max_val_in_idxx = idxx(r, c);
//         }
//         found_finite_in_idxx = true;
//       }
//     }
//   }

//   int max_iter_val = 0;
//   if (found_finite_in_idxx &&
//       max_val_in_idxx >=
//           1.0) { // Loop runs from 0 to max_iter_val-1, idxx is 1-based index
//     max_iter_val = static_cast<int>(std::floor(max_val_in_idxx));
//   }

//   if (max_iter_val <=
//       0) { // No valid iterations if max_val_in_idxx is < 1 or not found
//     return out;
//   }

//   for (int v_loop_idx = 0; v_loop_idx < max_iter_val; ++v_loop_idx) {
//     int current_shift = v_loop_idx;
//     double v_comparison_val = static_cast<double>(v_loop_idx);

//     DataFrame Tmp = feature_operators::ts_Delay(aa, current_shift);

//     // Create condition mask
//     // (idxx - 1.0) == v_comparison_val means we are looking for idxx ==
//     // v_loop_idx + 1 Also ensure idxx value itself is finite for the
//     // comparison.
//     for (Eigen::Index r = 0; r < aa.rows(); ++r) {
//       for (Eigen::Index c = 0; c < aa.cols(); ++c) {
//         if (std::isfinite(idxx(r, c)) &&
//             (std::floor(idxx(r, c)) - 1.0 == v_comparison_val)) {
//           // Check if Tmp(r,c) is not NaN before assignment,
//           // though ts_Delay propagates NaNs from aa.
//           // If aa had NaN at the source, Tmp will have NaN.
//           out(r, c) = Tmp(r, c);
//         }
//       }
//     }
//   }
//   return out;
// }

// } // namespace feature_operators
