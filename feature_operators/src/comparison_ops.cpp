#include "feature_operators/comparison_ops.hpp"
#include "feature_operators/types.hpp" // Added
#include <Eigen/Dense> // Kept for other Eigen types
#include <cassert> // for assert
#include <iostream>
namespace feature_operators {

// Array vs Array
DataFrame Mthan(const DataFrame& a, const DataFrame& b) {
    assert(a.rows() == b.rows() && a.cols() == b.cols());
    return (a > b).template cast<double>();
}

DataFrame MEthan(const DataFrame& a, const DataFrame& b) {
    assert(a.rows() == b.rows() && a.cols() == b.cols());
    return (a >= b).template cast<double>();
}

DataFrame Lthan(const DataFrame& a, const DataFrame& b) {
    assert(a.rows() == b.rows() && a.cols() == b.cols());
    return (a < b).template cast<double>();
}

DataFrame LEthan(const DataFrame& a, const DataFrame& b) {
    assert(a.rows() == b.rows() && a.cols() == b.cols());
    return (a <= b).template cast<double>();
}

DataFrame Equal(const DataFrame& a, const DataFrame& b) {
    assert(a.rows() == b.rows() && a.cols() == b.cols());
    return (a == b).template cast<double>();
}

DataFrame UnEqual(const DataFrame& a, const DataFrame& b) {
    assert(a.rows() == b.rows() && a.cols() == b.cols());
    return (a != b).template cast<double>();
}

// Array vs Scalar
DataFrame Mthan(const DataFrame& a, double b_scalar) {
    return (a > b_scalar).template cast<double>();
}

DataFrame MEthan(const DataFrame& a, double b_scalar) {
    return (a >= b_scalar).template cast<double>();
}

DataFrame Lthan(const DataFrame& a, double b_scalar) {
    return (a < b_scalar).template cast<double>();
}

DataFrame LEthan(const DataFrame& a, double b_scalar) {
    return (a <= b_scalar).template cast<double>();
}

DataFrame Equal(const DataFrame& a, double b_scalar) {
    return (a == b_scalar).template cast<double>();
}

DataFrame UnEqual(const DataFrame& a, double b_scalar) {
    return (a != b_scalar).template cast<double>();
}

} // namespace feature_operators
