// #include "feature_operators/core_math.hpp"
// #include "feature_operators/types.hpp" // Added
// #include <cmath> // For std::sqrt, std::log, etc. and isnan, isinf
// #include <limits> // For std::numeric_limits
// #include <Eigen/Dense> // Kept for other Eigen types

// namespace feature_operators {

// DataFrame Add(const DataFrame& a, const DataFrame& b) {
//     return a + b;
// }

// // DataFrame Minus(const DataFrame& a, const DataFrame& b) {
// //     return a - b;
// // }

// DataFrame Multiply(const DataFrame& a, const DataFrame& b) {
//     return a * b;
// }

// DataFrame Divide(const DataFrame& a, const DataFrame& b) {
//     return a / b;
// }

// DataFrame Sqrt(const DataFrame& a) {
//     return a.sqrt();
// }

// DataFrame log(const DataFrame& a) { // lowercase 'l'
//     return a.log();
// }

// DataFrame inv(const DataFrame& a) {
//     return 1.0 / a;
// }

// DataFrame Floor(const DataFrame& a) {
//     return a.floor();
// }

// DataFrame Ceil(const DataFrame& a) {
//     return a.ceil();
// }

// DataFrame Round(const DataFrame& a) {
//     return a.round();
// }

// DataFrame Abs(const DataFrame& a) {
//     return a.abs();
// }

// DataFrame Log(const DataFrame& a) { // capital 'L'
//     return a.unaryExpr([](double x) {
//         return (x > 0) ? std::log(x) : std::numeric_limits<double>::quiet_NaN();
//     });
// }

// DataFrame Sign(const DataFrame& a) {
//     return a.sign();
// }

// DataFrame Reverse(const DataFrame& a) {
//     return a * -1.0;
// }

// DataFrame Power(const DataFrame& base, double exponent) {
//     return base.pow(exponent);
// }

// DataFrame Exp(const DataFrame& a) {
//     return a.exp();
// }

// DataFrame SignedPower(const DataFrame& base, double exponent) {
//     return base.sign() * base.abs().pow(exponent);
// }

// DataFrame Softsign(const DataFrame& a) {
//     // Corrected implementation: a / (1 + sqrt(a.abs()))
//     // The original python code is: Divide(data, (1 + Abs(Sqrt(data))))
//     // This means we should take sqrt of data first, then abs of that result.
//     // Eigen's .sqrt() applied to an array will produce NaN for negative inputs.
//     // Eigen's .abs() applied to an array containing NaNs will keep them as NaNs.
//     return a / (1.0 + a.sqrt().abs());
// }

// } // namespace feature_operators
