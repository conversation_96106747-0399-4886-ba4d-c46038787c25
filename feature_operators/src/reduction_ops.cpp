// #include "feature_operators/reduction_ops.hpp"
// #include "feature_operators/types.hpp" // Added
// #include <Eigen/Dense> // Kept for other Eigen types
// #include <cmath>   // For std::isnan, std::fmin, std::fmax (or std::min/max with careful NaN handling)
// #include <limits>  // For std::numeric_limits
// #include <algorithm> // For std::min and std::max
// #include <cassert> // for assert

// namespace feature_operators {

// // Custom fmin-like behavior: if one is NaN, return the other; if both NaN, return NaN.
// inline double fmin_custom(double val_a, double val_b) {
//     if (std::isnan(val_a)) return val_b;
//     if (std::isnan(val_b)) return val_a;
//     return std::min(val_a, val_b);
// }

// // Custom fmax-like behavior: if one is NaN, return the other; if both NaN, return NaN.
// inline double fmax_custom(double val_a, double val_b) {
//     if (std::isnan(val_a)) return val_b;
//     if (std::isnan(val_b)) return val_a;
//     return std::max(val_a, val_b);
// }

// // Min
// DataFrame Min(const DataFrame& a, const DataFrame& b) {
//     assert(a.rows() == b.rows() && a.cols() == b.cols());
//     return a.binaryExpr(b, [](double val_a, double val_b) {
//         return fmin_custom(val_a, val_b);
//     });
// }

// DataFrame Min(const DataFrame& a, double b_scalar) {
//     return a.unaryExpr([b_scalar](double val_a) {
//         return fmin_custom(val_a, b_scalar);
//     });
// }

// DataFrame Min(double a_scalar, const DataFrame& b) {
//     return b.unaryExpr([a_scalar](double val_b) { // Note: applied unaryExpr on b
//         return fmin_custom(a_scalar, val_b);
//     });
// }

// // Max
// DataFrame Max(const DataFrame& a, const DataFrame& b) {
//     assert(a.rows() == b.rows() && a.cols() == b.cols());
//     return a.binaryExpr(b, [](double val_a, double val_b) {
//         return fmax_custom(val_a, val_b);
//     });
// }

// DataFrame Max(const DataFrame& a, double b_scalar) {
//     return a.unaryExpr([b_scalar](double val_a) {
//         return fmax_custom(val_a, b_scalar);
//     });
// }

// DataFrame Max(double a_scalar, const DataFrame& b) {
//     return b.unaryExpr([a_scalar](double val_b) { // Note: applied unaryExpr on b
//         return fmax_custom(a_scalar, val_b);
//     });
// }

// } // namespace feature_operators
