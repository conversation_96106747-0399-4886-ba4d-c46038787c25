#include "feature_operators/timeseries_ops.hpp"
#include "feature_operators/rolling_aggregations.hpp"
#include "feature_operators/types.hpp" // Added
#include <Eigen/Dense>                 // Kept for other Eigen types
#include <algorithm>                   // For std::max, std::min
#include <boost/accumulators/accumulators.hpp>
#include <boost/accumulators/statistics/count.hpp>
#include <boost/accumulators/statistics/kurtosis.hpp> // Added for kurtosis
#include <boost/accumulators/statistics/skewness.hpp>
#include <boost/accumulators/statistics/stats.hpp>
#include <boost/accumulators/statistics/variance.hpp> // Added for variance check
#include <boost/math/distributions/normal.hpp>
#include <cmath> // For std::isnan, std::sqrt
#include <deque>
#include <limits>    // For std::numeric_limits
#include <stdexcept> // For std::invalid_argument
#include <vector>

namespace feature_operators {

const double NaN = std::numeric_limits<double>::quiet_NaN();

DataFrame ts_Delay(const DataFrame &data, int n) {
  if (n < 0) {
    throw std::invalid_argument("ts_Delay: n must be non-negative.");
  }
  if (n == 0) {
    return data;
  }

  DataFrame result = DataFrame::Constant(data.rows(), data.cols(),
                                         NAN); // Use NAN for C++ standard
  if (n >= data.rows()) {
    return result;
  }

  // Optimized using Eigen's block operations
  // Copy the relevant part of 'data' to the shifted part of 'result'
  result.bottomRows(data.rows() - n) = data.topRows(data.rows() - n);

  return result;
}

DataFrame ts_Quantile(const DataFrame &data, int n_param, char rettype) {
  if (n_param < 0) {
    throw std::invalid_argument(
        "ts_Quantile: window size n must be non-negative.");
  }
  if (n_param == 0) { // Python: if n == 0: return s1
    return data;
  }

  int N_rows = data.rows();
  int N_cols = data.cols();
  int window_size = std::max(5, n_param); // Python: if NPrds < 5: NPrds = 5
  int min_periods = 1; // Match Python implementation: min_periods=1

  double q_val;
  switch (rettype) {
  case 'A':
    q_val = 0.2;
    break;
  case 'B':
    q_val = 0.4;
    break;
  case 'C':
    q_val = 0.6;
    break;
  case 'D':
    q_val = 0.8;
    break;
  default:
    throw std::invalid_argument(
        "ts_Quantile: rettype must be 'A', 'B', 'C', or 'D'.");
  }

  // The output will have the same number of rows as the input DataFrame
  DataFrame output = DataFrame::Constant(N_rows, N_cols, NaN);

  // Create start and end arrays for rolling window bounds
  std::vector<int64_t> start(N_rows), end(N_rows);
  for (int i = 0; i < N_rows; ++i) {
    start[i] = std::max(0, i - window_size + 1);
    end[i] = i + 1;
  }

  // Process each column independently
  for (int col = 0; col < N_cols; ++col) {
    // Iterate through each window
    for (int i = 0; i < N_rows; ++i) {
      int64_t s = start[i];
      int64_t e = end[i];

      // For quantile, we always recalculate from scratch since it requires
      // sorting
      std::vector<double> window_values;
      for (int64_t j = s; j < e && j < N_rows; ++j) {
        double val = data(j, col);
        if (!std::isnan(val)) {
          window_values.push_back(val);
        }
      }

      // Calculate quantile for the current window
      if (window_values.size() >= static_cast<size_t>(min_periods)) {
        std::sort(window_values.begin(), window_values.end());

        size_t N = window_values.size();
        if (N == 1) {
          output(i, col) = window_values[0];
        } else {
          // Linear interpolation for quantile
          double pos = q_val * (static_cast<double>(N) - 1.0);
          int idx_floor = static_cast<int>(std::floor(pos));
          int idx_ceil = static_cast<int>(std::ceil(pos));
          double frac = pos - static_cast<double>(idx_floor);

          // Ensure indices are within bounds
          idx_floor = std::max(0, std::min(static_cast<int>(N - 1), idx_floor));
          idx_ceil = std::max(0, std::min(static_cast<int>(N - 1), idx_ceil));

          if (idx_floor == idx_ceil) { // pos is an integer
            output(i, col) = window_values[idx_floor];
          } else {
            output(i, col) = window_values[idx_floor] * (1.0 - frac) +
                             window_values[idx_ceil] * frac;
          }
        }
      } else {
        output(i, col) = NaN;
      }
    }
  }
  return output;
}

DataFrame ts_MaxDD(const DataFrame &data, int n_param) {
  int n_effective = std::max(3, n_param);
  DataFrame result = DataFrame::Constant(data.rows(), data.cols(), NaN);

  for (Eigen::Index j = 0; j < data.cols(); ++j) { // Iterate over columns
    double current_col_peak =
        -std::numeric_limits<double>::infinity(); // Tracks peak for the current
                                                  // column up to current window
    bool first_peak_found_in_col = false;

    for (Eigen::Index i = 0; i < data.rows();
         ++i) { // Iterate over rows (time points)
      if (i < n_effective - 1) {
        result(i, j) = NaN; // Not enough history for a full window
        // Update peak with current value for next iterations if needed by other
        // logic, but for MaxDD, we only care about window-specific peaks. The
        // python logic re-initializes pMax for each window.
        continue;
      }

      Eigen::ArrayXd window_segment =
          data.col(j).segment(i - n_effective + 1, n_effective);

      double window_peak = -std::numeric_limits<double>::infinity();
      double max_drawdown_in_window = 0.0;
      bool found_non_nan_in_window = false;

      // First pass to find the initial peak in the window if not all NaNs
      // This helps establish a valid peak before calculating drawdowns.
      // The python code implicitly does this by `pMax = np.maximum(pMax,
      // window_data[j])` where pMax starts very small.

      // Let's follow python logic: peak is evolving within the window
      double pMax_in_window_iter = -std::numeric_limits<double>::infinity();
      // Python `pMax = 1e-10` is a floor. Let's use a more robust evolving
      // peak. For the first element of the window, pMax is that element (if not
      // NaN). Then for subsequent, it's max(previous_pMax, current_element).

      bool first_val_in_window_for_peak_init = true;

      for (Eigen::Index k = 0; k < window_segment.size(); ++k) {
        double val_k = window_segment(k);
        double current_step_drawdown = 0.0;

        if (!std::isnan(val_k)) {
          found_non_nan_in_window = true;
          if (first_val_in_window_for_peak_init) {
            pMax_in_window_iter = val_k;
            first_val_in_window_for_peak_init = false;
          } else {
            pMax_in_window_iter = std::max(pMax_in_window_iter, val_k);
          }

          if (pMax_in_window_iter >
              1e-9) { // Avoid division by zero/small and ensure positive peak
            current_step_drawdown = 1.0 - val_k / pMax_in_window_iter;
          }
        }
        // If val_k is NaN, current_step_drawdown remains 0.
        // This matches `pMaxDD[np.isnan(pMaxDD)] = 0` because the drawdown term
        // itself is 0.
        max_drawdown_in_window =
            std::max(max_drawdown_in_window, current_step_drawdown);
      }

      if (!found_non_nan_in_window && (i >= n_effective - 1)) {
        // All NaNs in window, python gives 0 for MaxDD.
        result(i, j) = 0.0;
      } else if (i >=
                 n_effective - 1) { // If any non-NaN, use calculated MaxDD.
        result(i, j) = max_drawdown_in_window;
      }
      // else it's NaN because i < n_effective -1
    }
  }
  return result;
}

DataFrame ts_ChgRate(const DataFrame &data, int n_param) {
  int n_effective = std::max(1, n_param); // Python: if NPrds<1: NPrds = 1

  DataFrame result = DataFrame::Constant(data.rows(), data.cols(), NaN);

  for (Eigen::Index j = 0; j < data.cols(); ++j) { // Iterate over columns
    for (Eigen::Index i = 0; i < data.rows();
         ++i) { // Iterate over rows (time points)
      if (i < n_effective) {
        result(i, j) = NaN; // Not enough history for the lagged value
        continue;
      }

      double current_val = data(i, j);
      double lagged_val = data(i - n_effective, j);

      if (std::isnan(current_val) || std::isnan(lagged_val)) {
        result(i, j) = NaN;
      } else if (lagged_val == 0.0) {
        if (current_val == 0.0) {
          result(i, j) = NaN; // (0/0) - 1 -> NaN
        } else if (current_val > 0.0) {
          result(i, j) =
              std::numeric_limits<double>::infinity(); // (positive/0)
                                                       // - 1 -> inf
        } else {                                       // current_val < 0.0
          result(i, j) =
              -std::numeric_limits<double>::infinity(); // (negative/0) - 1 ->
                                                        // -inf
        }
      } else {
        result(i, j) = (current_val / lagged_val) - 1.0;
      }
    }
  }
  return result;
}

DataFrame ts_Divide(const DataFrame &data, int n_param) {
  int n_effective = std::max(5, n_param);

  DataFrame result = DataFrame::Constant(data.rows(), data.cols(), NaN);

  for (Eigen::Index j = 0; j < data.cols(); ++j) { // Iterate over columns
    for (Eigen::Index i = 0; i < data.rows();
         ++i) { // Iterate over rows (time points)
      if (i < n_effective) {
        result(i, j) = NaN; // Not enough history for the lagged value
        continue;
      }

      double numerator = data(i, j);
      double denominator = data(i - n_effective, j);

      if (std::isnan(numerator) || std::isnan(denominator)) {
        result(i, j) = NaN;
      } else if (denominator == 0.0) {
        if (numerator == 0.0) {
          result(i, j) = NaN; // 0/0
        } else if (numerator > 0.0) {
          result(i, j) = std::numeric_limits<double>::infinity();
        } else { // numerator < 0.0
          result(i, j) = -std::numeric_limits<double>::infinity();
        }
      } else {
        result(i, j) = numerator / denominator;
      }
    }
  }
  return result;
}

DataFrame ts_Product(const DataFrame &data, int n_param) {
  int n_effective = std::max(3, n_param);
  // min_periods = 1 is implicit. Product of empty set is 1.

  DataFrame result = DataFrame::Constant(data.rows(), data.cols(), NaN);

  for (Eigen::Index j = 0; j < data.cols(); ++j) { // Iterate over columns
    for (Eigen::Index i = 0; i < data.rows();
         ++i) { // Iterate over rows (time points)
      Eigen::Index start_row =
          std::max(static_cast<Eigen::Index>(0), i - n_effective + 1);
      auto window_segment_expr =
          data.col(j).segment(start_row, i - start_row + 1);

      if (window_segment_expr.size() == 0) {
        result(i, j) = 1.0; // Product of empty set is 1
        continue;
      }

      Eigen::ArrayXd temp_window_array = window_segment_expr;

      // Replace NaNs with 1.0. Eigen's select can take a scalar for the 'then'
      // or 'else' part.
      temp_window_array =
          temp_window_array.isNaN().select(1.0, temp_window_array);

      result(i, j) = temp_window_array.prod();
    }
  }
  return result;
}

DataFrame ts_Sum(const DataFrame &data, int n_param) {
  if (n_param < 0) {
    throw std::invalid_argument("ts_Sum: window size n must be non-negative.");
  }
  if (n_param == 0) { // Python: if n == 0: return s1
    return data;
  }

  int N_rows = data.rows();
  int N_cols = data.cols();
  int window_size = std::max(3, n_param); // Python: if NPrds < 3: NPrds = 3
  int min_periods = 1; // Match Python implementation: min_periods=1

  // The output will have the same number of rows as the input DataFrame
  DataFrame output = DataFrame::Constant(N_rows, N_cols, NaN);

  // Create start and end arrays for rolling window bounds
  std::vector<int64_t> start(N_rows), end(N_rows);
  for (int i = 0; i < N_rows; ++i) {
    start[i] = std::max(0, i - window_size + 1);
    end[i] = i + 1;
  }

  // Check if bounds are monotonic increasing for optimization
  bool is_monotonic_increasing_bounds =
      rolling::is_monotonic_increasing_start_end_bounds(start, end);

  // Process each column independently
  for (int col = 0; col < N_cols; ++col) {
    // Initialize state for the rolling calculation
    int64_t nobs = 0;
    double sum_x = 0.0;
    double compensation_add = 0.0, compensation_remove = 0.0;

    // Iterate through each window
    for (int i = 0; i < N_rows; ++i) {
      int64_t s = start[i];
      int64_t e = end[i];

      // Over the first window, or when bounds are not monotonic, or when
      // there's no overlap
      if (i == 0 || !is_monotonic_increasing_bounds || s >= end[i - 1]) {
        // Reset and recalculate from scratch
        compensation_add = compensation_remove = 0.0;
        sum_x = 0.0;
        nobs = 0;

        for (int64_t j = s; j < e && j < N_rows; ++j) {
          rolling::add_sum(data(j, col), nobs, sum_x, compensation_add);
        }
      } else {
        // Incremental calculation: remove old values and add new values

        // Calculate deletes
        for (int64_t j = start[i - 1]; j < s && j < N_rows; ++j) {
          rolling::remove_sum(data(j, col), nobs, sum_x, compensation_remove);
        }

        // Calculate adds
        for (int64_t j = end[i - 1]; j < e && j < N_rows; ++j) {
          rolling::add_sum(data(j, col), nobs, sum_x, compensation_add);
        }
      }

      // Calculate sum for the current window
      output(i, col) = rolling::calc_sum(min_periods, nobs, sum_x);

      // Reset state if bounds are not monotonic
      if (!is_monotonic_increasing_bounds) {
        nobs = 0;
        sum_x = 0.0;
        compensation_remove = 0.0;
      }
    }
  }
  return output;
}

DataFrame ts_Argmax(const DataFrame &data, int n_param) {
  int n_effective = std::max(3, n_param);
  // min_periods = 1 is implicit.

  DataFrame result = DataFrame::Constant(data.rows(), data.cols(), NaN);

  for (Eigen::Index j = 0; j < data.cols(); ++j) { // Iterate over columns
    for (Eigen::Index i = 0; i < data.rows();
         ++i) { // Iterate over rows (time points)
      Eigen::Index start_row =
          std::max(static_cast<Eigen::Index>(0), i - n_effective + 1);
      // The actual window considered for argmax is from start_row to i. Its
      // length is (i - start_row + 1). However, the result calculation
      // n_effective - idx uses n_effective.
      Eigen::ArrayXd window_segment =
          data.col(j).segment(start_row, i - start_row + 1);

      double max_val = -std::numeric_limits<double>::infinity();
      int argmax_idx = -1; // 0-based index within the window_segment

      for (Eigen::Index k = 0; k < window_segment.size(); ++k) {
        double current_val = window_segment(k);
        double val_for_comp = std::isnan(current_val)
                                  ? -std::numeric_limits<double>::infinity()
                                  : current_val;

        if (val_for_comp > max_val) {
          max_val = val_for_comp;
          argmax_idx = k;
        }
      }

      if (argmax_idx != -1) { // If at least one non-NaN (or treated as non-NaN)
                              // value was found
        // The index argmax_idx is relative to the start of window_segment.
        // The Python formula is NPrds - result_of_argmax. result_of_argmax is
        // 0-based index. NPrds is n_effective.
        result(i, j) = static_cast<double>(n_effective - argmax_idx);
      } else {
        // All values in window were NaN
        result(i, j) = NaN;
      }
    }
  }
  return result;
}

DataFrame ts_Argmin(const DataFrame &data, int n_param) {
  int n_effective = std::max(3, n_param);
  // min_periods = 1 is implicit.

  DataFrame result = DataFrame::Constant(data.rows(), data.cols(), NaN);

  for (Eigen::Index j = 0; j < data.cols(); ++j) { // Iterate over columns
    for (Eigen::Index i = 0; i < data.rows();
         ++i) { // Iterate over rows (time points)
      Eigen::Index start_row =
          std::max(static_cast<Eigen::Index>(0), i - n_effective + 1);
      Eigen::ArrayXd window_segment =
          data.col(j).segment(start_row, i - start_row + 1);

      double min_val = std::numeric_limits<double>::infinity();
      int argmin_idx = -1; // 0-based index within the window_segment

      for (Eigen::Index k = 0; k < window_segment.size(); ++k) {
        double current_val = window_segment(k);
        double val_for_comp = std::isnan(current_val)
                                  ? std::numeric_limits<double>::infinity()
                                  : current_val;

        if (val_for_comp < min_val) {
          min_val = val_for_comp;
          argmin_idx = k;
        }
      }

      if (argmin_idx != -1) {
        result(i, j) = static_cast<double>(n_effective - argmin_idx);
      } else {
        // All values in window were NaN
        result(i, j) = NaN;
      }
    }
  }
  return result;
}

DataFrame ts_Median(const DataFrame &data, int n_param) {
  if (n_param < 0) {
    throw std::invalid_argument(
        "ts_Median: window size n must be non-negative.");
  }
  if (n_param == 0) { // Python: if n == 0: return s1
    return data;
  }

  int N_rows = data.rows();
  int N_cols = data.cols();
  int window_size = std::max(3, n_param); // Python: if NPrds < 3: NPrds = 3
  int min_periods = 1; // Match Python implementation: min_periods=1

  // The output will have the same number of rows as the input DataFrame
  DataFrame output = DataFrame::Constant(N_rows, N_cols, NaN);

  // Create start and end arrays for rolling window bounds
  std::vector<int64_t> start(N_rows), end(N_rows);
  for (int i = 0; i < N_rows; ++i) {
    start[i] = std::max(0, i - window_size + 1);
    end[i] = i + 1;
  }

  // Process each column independently
  for (int col = 0; col < N_cols; ++col) {
    // Iterate through each window
    for (int i = 0; i < N_rows; ++i) {
      int64_t s = start[i];
      int64_t e = end[i];

      // For median, we always recalculate from scratch since it requires
      // sorting
      std::vector<double> window_values;
      for (int64_t j = s; j < e && j < N_rows; ++j) {
        double val = data(j, col);
        if (!std::isnan(val)) {
          window_values.push_back(val);
        }
      }

      // Calculate median for the current window
      if (window_values.size() >= static_cast<size_t>(min_periods)) {
        std::sort(window_values.begin(), window_values.end());

        size_t size = window_values.size();
        if (size % 2 == 1) {
          // Odd number of elements, take the middle one
          output(i, col) = window_values[size / 2];
        } else {
          // Even number of elements, take the average of the two middle ones
          output(i, col) =
              (window_values[size / 2 - 1] + window_values[size / 2]) / 2.0;
        }
      } else {
        output(i, col) = NaN;
      }
    }
  }
  return output;
}

DataFrame ts_Rank(const DataFrame &data, int n_param) {
  int n_effective = std::max(5, n_param);
  // min_periods = 1 is implicit in how rank is calculated (if window_values is
  // not empty).

  DataFrame result = DataFrame::Constant(data.rows(), data.cols(), NaN);

  for (Eigen::Index j = 0; j < data.cols(); ++j) { // Iterate over columns
    for (Eigen::Index i = 0; i < data.rows();
         ++i) { // Iterate over rows (time points)
      double current_value_at_i_j = data(i, j);
      if (std::isnan(current_value_at_i_j)) {
        result(i, j) = NaN;
        continue;
      }

      Eigen::Index start_row =
          std::max(static_cast<Eigen::Index>(0), i - n_effective + 1);
      Eigen::ArrayXd window_segment =
          data.col(j).segment(start_row, i - start_row + 1);

      std::vector<double> window_values;
      for (Eigen::Index k = 0; k < window_segment.size(); ++k) {
        if (!std::isnan(window_segment(k))) {
          window_values.push_back(window_segment(k));
        }
      }

      if (window_values.empty()) {
        result(i, j) = NaN;
        continue;
      }

      double num_less = 0;
      double num_equal = 0;
      for (double val_in_window : window_values) {
        if (val_in_window < current_value_at_i_j) {
          num_less++;
        } else if (val_in_window == current_value_at_i_j) {
          num_equal++;
        }
      }

      double rank_avg = (num_less + 1.0) + (num_equal - 1.0) / 2.0;
      double pct_rank = rank_avg / static_cast<double>(window_values.size());

      result(i, j) = pct_rank - (0.5 / static_cast<double>(n_effective));
    }
  }
  return result;
}

DataFrame ts_Decay(const DataFrame &data, int n_param) {
  if (n_param <= 0) { // Or throw error, problem implies n >= 1 from context
    return DataFrame::Constant(data.rows(), data.cols(), NaN);
  }
  int n_effective = n_param;

  if (n_effective == 1) {
    return data;
  }

  DataFrame result = DataFrame::Constant(data.rows(), data.cols(), NaN);

  std::vector<double> weights_raw(n_effective);
  double sum_weights_raw = 0;
  for (int k = 0; k < n_effective; ++k) {
    // Weights k+1 for window elements from oldest to newest
    // k=0 is oldest (weight 1), k=n_effective-1 is newest (weight n_effective)
    weights_raw[k] = static_cast<double>(k + 1);
    sum_weights_raw += weights_raw[k];
  }

  std::vector<double> weights_normalized(n_effective);
  if (sum_weights_raw == 0) { // Should not happen if n_effective > 0
    return DataFrame::Constant(data.rows(), data.cols(), NaN);
  }
  for (int k = 0; k < n_effective; ++k) {
    weights_normalized[k] = weights_raw[k] / sum_weights_raw;
  }

  for (Eigen::Index j = 0; j < data.cols(); ++j) {   // Iterate over columns
    for (Eigen::Index i = 0; i < data.rows(); ++i) { // Iterate over rows
      if (i < n_effective - 1) {
        result(i, j) = NaN;
        continue;
      }

      Eigen::ArrayXd window_segment =
          data.col(j).segment(i - n_effective + 1, n_effective);
      double current_weighted_sum = 0;
      bool has_nan_in_window = false;

      for (int k = 0; k < n_effective; ++k) {
        if (std::isnan(window_segment(k))) {
          has_nan_in_window = true;
          break;
        }
        current_weighted_sum += window_segment(k) * weights_normalized[k];
      }

      if (has_nan_in_window) {
        result(i, j) = NaN;
      } else {
        result(i, j) = current_weighted_sum;
      }
    }
  }
  return result;
}

DataFrame ts_Decay2(const DataFrame &data, int n_param) {
  if (n_param <= 0) {
    return DataFrame::Constant(data.rows(), data.cols(), NaN);
  }
  int n_effective = n_param;

  if (n_effective == 1) {
    return data;
  }

  DataFrame result = DataFrame::Constant(data.rows(), data.cols(), NaN);

  double alpha = 1.0 - 2.0 / (static_cast<double>(n_effective) + 1.0);
  if (std::abs(alpha) < 1e-14 ||
      std::abs(alpha - 1.0) < 1e-14) { // alpha is 0 or 1
    // If alpha is 0, all weights except alpha^0 (if used) would be 0.
    // If alpha is 1 (e.g. n_effective approaches infinity, not possible here,
    // or n_effective=1 which is handled) Let's handle simple cases or specific
    // alpha values. If alpha = 0 (when n_effective = 1, handled), weights are
    // tricky. The problem implies n_effective >= 1. Python weights: alpha^n,
    // alpha^(n-1), ..., alpha^1. Newest is alpha^1. So, window_segment(k)
    // corresponds to data.shift(n_effective - 1 - k) data.shift(n-1) is oldest,
    // gets weight alpha^n data.shift(0) is newest, gets weight alpha^1
    // window_segment(0) is oldest, window_segment(n_effective-1) is newest.
    // So, window_segment(k) should be multiplied by
    // weight_for_age_(n_effective-1-k). Let w_idx be power: n_effective,
    // n_effective-1, ..., 1. weight_for_window_idx[k] = alpha^(n_effective - k)
    // for k = 0 (oldest) to n_effective-1 (newest). Example n=3: alpha^(3),
    // alpha^(2), alpha^(1). window_segment(0) * alpha^3, window_segment(1) *
    // alpha^2, window_segment(2) * alpha^1
  }

  std::vector<double> weights_raw(n_effective);
  double sum_weights_raw = 0;
  for (int k = 0; k < n_effective; ++k) {
    // Python w = [alpha^n, ..., alpha^1]. Newest is alpha^1.
    // window_segment[0] is oldest, window_segment[n-1] is newest.
    // Oldest (idx 0) gets weight alpha^n. Newest (idx n-1) gets weight alpha^1.
    weights_raw[k] = std::pow(alpha, static_cast<double>(n_effective - k));
    sum_weights_raw += weights_raw[k];
  }

  std::vector<double> weights_normalized(n_effective);
  if (sum_weights_raw == 0 &&
      n_effective > 0) { // Avoid division by zero if all weights became zero
                         // (e.g. alpha=0)
    // If alpha is 0, only weight for alpha^0 (if definition was different)
    // would be 1. With current def alpha^n ... alpha^1: if alpha=0, all weights
    // are 0 unless n=1 (alpha^1=0). If n_effective > 0 and sum_weights_raw is 0
    // (e.g. alpha=0), result should be NaN or 0 based on context. Python's
    // default for sum(weights)=0 in weighted average is often NaN or error. If
    // alpha=0, for n_eff > 1, all weights are 0. sum_weights_raw = 0. Result
    // NaN.
    return DataFrame::Constant(data.rows(), data.cols(), NaN);
  }
  if (n_effective > 0 &&
      sum_weights_raw != 0) { // only normalize if sum is not zero
    for (int k = 0; k < n_effective; ++k) {
      weights_normalized[k] = weights_raw[k] / sum_weights_raw;
    }
  }

  for (Eigen::Index j = 0; j < data.cols(); ++j) {   // Iterate over columns
    for (Eigen::Index i = 0; i < data.rows(); ++i) { // Iterate over rows
      if (i < n_effective - 1) {
        result(i, j) = NaN;
        continue;
      }

      Eigen::ArrayXd window_segment =
          data.col(j).segment(i - n_effective + 1, n_effective);
      double current_weighted_sum = 0;
      bool has_nan_in_window = false;

      if (sum_weights_raw ==
          0) { // If sum_weights is zero (e.g. alpha=0, n_eff > 1)
        result(i, j) =
            NaN; // Or 0.0 if data is not all NaN. Python context implies NaN.
        continue;
      }

      for (int k = 0; k < n_effective; ++k) {
        if (std::isnan(window_segment(k))) {
          has_nan_in_window = true;
          break;
        }
        current_weighted_sum += window_segment(k) * weights_normalized[k];
      }

      if (has_nan_in_window) {
        result(i, j) = NaN;
      } else {
        result(i, j) = current_weighted_sum;
      }
    }
  }
  return result;
}

// Required for ts_TransNorm

// Using a slightly larger epsilon for clamping to avoid issues at the exact
// boundaries 0 and 1 This is defined in panel_ops.cpp, ensure it's accessible
// or redefine. For safety, let's define it if not found.
#ifndef CLAMP_EPSILON
const double CLAMP_EPSILON_TS = 1e-9;
#else
const double CLAMP_EPSILON_TS = CLAMP_EPSILON;
#endif

DataFrame ts_TransNorm(const DataFrame &data, int n_param) {
  int n_effective = std::max(3, n_param); // Python: if n < 3: n = 3
  // min_periods for rank is 1, but overall transformation needs n_effective.

  DataFrame result = DataFrame::Constant(data.rows(), data.cols(), NaN);
  boost::math::normal dist(
      0.0, 1.0); // Standard normal distribution for ppf (quantile)

  for (Eigen::Index j = 0; j < data.cols(); ++j) { // Iterate over columns
    for (Eigen::Index i = 0; i < data.rows();
         ++i) { // Iterate over rows (time points)
      double current_value_at_i_j = data(i, j);
      if (std::isnan(current_value_at_i_j)) {
        result(i, j) = NaN;
        continue;
      }

      Eigen::Index start_row =
          std::max(static_cast<Eigen::Index>(0), i - n_effective + 1);
      Eigen::ArrayXd window_segment =
          data.col(j).segment(start_row, i - start_row + 1);

      std::vector<double> window_values;
      for (Eigen::Index k = 0; k < window_segment.size(); ++k) {
        if (!std::isnan(window_segment(k))) {
          window_values.push_back(window_segment(k));
        }
      }

      if (window_values.empty()) {
        result(i, j) = NaN;
        continue;
      }

      // Calculate rank of current_value_at_i_j within window_values
      // Pandas default rank method is 'average'
      double num_less = 0;
      double num_equal = 0;
      for (double val_in_window : window_values) {
        if (val_in_window < current_value_at_i_j) {
          num_less++;
        } else if (val_in_window == current_value_at_i_j) {
          num_equal++;
        }
      }

      // Average rank for ties: (rank of first item in tied group) + (num_equal
      // - 1) / 2.0 Rank of first item in tied group is num_less + 1
      double rank_avg = (num_less + 1.0) + (num_equal - 1.0) / 2.0;
      double pct_rank = rank_avg / static_cast<double>(window_values.size());

      // Transform rank: rank_pct - (0.5 / n_eff)
      // The python code is `data.rolling().rank(pct=True) - 1/n/2`. Here n is
      // n_effective.
      double transformed_rank =
          pct_rank - (0.5 / static_cast<double>(n_effective));

      // Clamp transformed_rank to (epsilon, 1-epsilon)
      double clamped_rank = std::max(
          CLAMP_EPSILON_TS, std::min(transformed_rank, 1.0 - CLAMP_EPSILON_TS));

      if (clamped_rank <= 0.0 ||
          clamped_rank >= 1.0) { // Safeguard after clamping
        result(i, j) = NaN;
      } else {
        result(i, j) = boost::math::quantile(dist, clamped_rank);
      }
    }
  }
  return result;
}

DataFrame ts_Partial_corr(const DataFrame &data1, const DataFrame &data2,
                          const DataFrame &data3, int n_param) {
  if (data1.rows() != data2.rows() || data1.cols() != data2.cols() ||
      data1.rows() != data3.rows() || data1.cols() != data3.cols()) {
    throw std::invalid_argument("ts_Partial_corr: All three input DataFrames "
                                "must have the same dimensions.");
  }

  // n_param will be passed to ts_Corr, which handles n_effective (max(2,n)) and
  // min_periods (2)
  DataFrame corr12 = ts_Corr(data1, data2, n_param);
  DataFrame corr13 = ts_Corr(data1, data3, n_param);
  DataFrame corr23 = ts_Corr(data2, data3, n_param);

  DataFrame result = DataFrame::Constant(data1.rows(), data1.cols(), NaN);

  for (Eigen::Index j = 0; j < data1.cols(); ++j) {
    for (Eigen::Index i = 0; i < data1.rows(); ++i) {
      double c12 = corr12(i, j);
      double c13 = corr13(i, j);
      double c23 = corr23(i, j);

      if (std::isnan(c12) || std::isnan(c13) || std::isnan(c23)) {
        result(i, j) = NaN;
        continue;
      }

      double numerator = c12 - c13 * c23;

      double den_term1_sq = 1.0 - c13 * c13;
      double den_term2_sq = 1.0 - c23 * c23;

      // Clamp to avoid sqrt of small negative due to precision for |corr| > 1
      if (den_term1_sq < 0)
        den_term1_sq = 0;
      if (den_term2_sq < 0)
        den_term2_sq = 0;

      double denominator = std::sqrt(den_term1_sq) * std::sqrt(den_term2_sq);

      if (std::abs(denominator) < 1e-14) {
        result(i, j) =
            NaN; // Avoid division by zero; Python replaces inf with NaN
      } else {
        double partial_c = numerator / denominator;
        if (std::isinf(partial_c)) {
          result(i, j) = NaN;
        } else {
          // Clamp result to [-1, 1] for safety, though mathematically should be
          // if inputs are valid correlations
          result(i, j) = std::max(-1.0, std::min(1.0, partial_c));
        }
      }
    }
  }
  return result;
}

DataFrame ts_Entropy(const DataFrame &data, int n_param, int bucket_count) {
  if (bucket_count <= 0) {
    throw std::invalid_argument("ts_Entropy: bucket_count must be positive.");
  }
  int n_effective = std::max(1, n_param);
  int min_periods = 3; // Python code: if n_valid > 2

  DataFrame result = DataFrame::Constant(data.rows(), data.cols(), NaN);

  for (Eigen::Index j = 0; j < data.cols(); ++j) { // Iterate over columns
    for (Eigen::Index i = 0; i < data.rows();
         ++i) { // Iterate over rows (time points)
      Eigen::Index start_row =
          std::max(static_cast<Eigen::Index>(0), i - n_effective + 1);
      Eigen::ArrayXd window_segment =
          data.col(j).segment(start_row, i - start_row + 1);

      std::vector<double> hist_data;
      for (Eigen::Index k = 0; k < window_segment.size(); ++k) {
        if (!std::isnan(window_segment(k))) {
          hist_data.push_back(window_segment(k));
        }
      }

      if (hist_data.size() < static_cast<size_t>(min_periods)) {
        continue; // result(i, j) remains NaN
      }

      double min_val = hist_data[0];
      double max_val = hist_data[0];
      for (size_t k = 1; k < hist_data.size(); ++k) {
        if (hist_data[k] < min_val)
          min_val = hist_data[k];
        if (hist_data[k] > max_val)
          max_val = hist_data[k];
      }

      // Removed: if (std::abs(max_val - min_val) < 1e-14) check

      std::vector<int> counts(bucket_count, 0);
      double bin_width =
          (max_val - min_val) / static_cast<double>(bucket_count);
      // Removed: if (bin_width == 0) check
      // If max_val == min_val, then bin_width will be 0.
      // In this case, (val - min_val) / bin_width would be 0/0 = NaN for val ==
      // min_val, or (positive_num / 0) = Inf for val > min_val (not possible if
      // all same). The bin_index calculation needs to handle bin_width = 0 if
      // it occurs. However, if all values are the same, min_val == max_val. All
      // values will fall into one conceptual bin. hist_data will have all same
      // values. counts[some_bin_idx] = num_valid_points. pk = 1.
      // sum_val_log_val = 1*log(1) = 0. Result: log(bucket_count) - 0 =
      // log(bucket_count).

      for (double val : hist_data) {
        int bin_index;
        if (bin_width == 0) { // Handles case where max_val == min_val
          bin_index = 0;      // All values go to the first bin
        } else {
          bin_index = static_cast<int>((val - min_val) / bin_width);
        }
        // Clamp bin_index: values equal to max_val should go into the last
        // bucket.
        if (bin_index >= bucket_count)
          bin_index = bucket_count - 1;
        if (bin_index < 0)
          bin_index = 0;
        counts[bin_index]++;
      }

      double sum_val_log_val = 0.0;
      double num_valid_points = static_cast<double>(hist_data.size());

      for (int count : counts) {
        if (count > 0) {
          double p_k = static_cast<double>(count) / num_valid_points;
          sum_val_log_val += p_k * std::log(p_k);
        }
      }

      if (bucket_count ==
          0) { // Should be caught by the initial check, but as a safeguard
        result(i, j) = NaN;
      } else {
        result(i, j) =
            std::log(static_cast<double>(bucket_count)) -
            (1.0 / static_cast<double>(bucket_count)) * sum_val_log_val;
      }
    }
  }
  return result;
}

DataFrame ts_Regression(const DataFrame &data1, const DataFrame &data2,
                        int n_param, char rettype) {
  if (data1.rows() != data2.rows() || data1.cols() != data2.cols()) {
    throw std::invalid_argument(
        "ts_Regression: data1 and data2 must have the same dimensions.");
  }
  if (rettype != 'A' && rettype != 'B' && rettype != 'C' && rettype != 'D') {
    throw std::invalid_argument(
        "ts_Regression: rettype must be 'A', 'B', 'C', or 'D'.");
  }

  int n_effective = std::max(3, n_param); // Window size for calculation
  int min_valid_pairs_for_beta =
      2; // Minimum number of valid pairs for beta calculation

  DataFrame result = DataFrame::Constant(data1.rows(), data1.cols(),
                                         NaN); // Initialize with NaN

  for (Eigen::Index j = 0; j < data1.cols(); ++j) { // Iterate over columns
    double current_sum_x = 0.0;
    double current_sum_y = 0.0;
    double current_sum_x_sq = 0.0;
    double current_sum_xy = 0.0;
    int current_valid_pairs_count = 0;

    for (Eigen::Index i = 0; i < data1.rows();
         ++i) { // Iterate over rows (time points)
      // Add incoming pair
      double val_y_in = data1(i, j);
      double val_x_in = data2(i, j);
      if (!std::isnan(val_y_in) && !std::isnan(val_x_in)) {
        current_sum_x += val_x_in;
        current_sum_y += val_y_in;
        current_sum_x_sq += val_x_in * val_x_in;
        current_sum_xy += val_x_in * val_y_in;
        current_valid_pairs_count++;
      }

      // Subtract outgoing pair if window has reached max length n_effective
      if (i >= n_effective) {
        double val_y_out = data1(i - n_effective, j);
        double val_x_out = data2(i - n_effective, j);
        if (!std::isnan(val_y_out) && !std::isnan(val_x_out)) {
          current_sum_x -= val_x_out;
          current_sum_y -= val_y_out;
          current_sum_x_sq -= val_x_out * val_x_out;
          current_sum_xy -= val_x_out * val_y_out;
          current_valid_pairs_count--;
        }
      }

      double beta = NaN;
      double alpha = NaN;

      if (current_valid_pairs_count >= min_valid_pairs_for_beta) {
        double N_double = static_cast<double>(current_valid_pairs_count);
        double mean_x = current_sum_x / N_double;
        double mean_y = current_sum_y / N_double;
        double mean_xy = current_sum_xy / N_double;

        double var_x_pop = (current_sum_x_sq / N_double) - (mean_x * mean_x);

        if (std::abs(var_x_pop) < 1e-14) {
          beta = NaN;
        } else {
          beta = (mean_xy - mean_x * mean_y) / var_x_pop;
          if (std::isinf(beta)) {
            beta = NaN;
          }
        }

        if (!std::isnan(beta)) {
          alpha = mean_y - beta * mean_x;
        }
      }

      // Assign based on rettype
      if (rettype == 'A') {
        result(i, j) = beta;
      } else if (rettype == 'B') {
        result(i, j) = alpha;
      } else if (rettype == 'C') {
        if (!std::isnan(alpha) && !std::isnan(beta) &&
            !std::isnan(data2(i, j))) {
          result(i, j) = alpha + beta * data2(i, j);
        } else {
          // result(i, j) is already NaN if alpha or beta is NaN due to
          // initialization
        }
      } else if (rettype == 'D') {
        if (!std::isnan(alpha) && !std::isnan(beta) &&
            !std::isnan(data1(i, j)) && !std::isnan(data2(i, j))) {
          result(i, j) = data1(i, j) - (alpha + beta * data2(i, j));
        } else {
          // result(i, j) is already NaN
        }
      }
      // If beta/alpha are NaN and rettype needs them, result(i,j) remains NaN
      // (from initialization)
    }
  }
  return result;
}

DataFrame ts_Corr(const DataFrame &data1, const DataFrame &data2, int n_param) {
  if (data1.rows() != data2.rows() || data1.cols() != data2.cols()) {
    throw std::invalid_argument(
        "ts_Corr: data1 and data2 must have the same dimensions.");
  }

  int n_effective = (n_param <= 1) ? 2 : n_param;
  int min_periods = 2;

  DataFrame result = DataFrame::Constant(data1.rows(), data1.cols(), NaN);

  for (Eigen::Index j = 0; j < data1.cols(); ++j) {
    double current_sum_x = 0.0, c_sum_x = 0.0;
    double current_sum_y = 0.0, c_sum_y = 0.0;
    double current_sum_x_sq = 0.0, c_sum_x_sq = 0.0;
    double current_sum_y_sq = 0.0, c_sum_y_sq = 0.0;
    double current_sum_xy = 0.0, c_sum_xy = 0.0;
    int current_valid_pairs_count = 0;

    for (Eigen::Index i = 0; i < data1.rows(); ++i) {
      // Add incoming pair
      double val1_in = data1(i, j);
      double val2_in = data2(i, j);
      if (!std::isnan(val1_in) && !std::isnan(val2_in)) {
        current_valid_pairs_count++;
        double val1_in_sq = val1_in * val1_in;
        double val2_in_sq = val2_in * val2_in;
        double val_xy_in = val1_in * val2_in;

        // Kahan sum for sum_x
        double y = val1_in - c_sum_x;
        double t = current_sum_x + y;
        c_sum_x = (t - current_sum_x) - y;
        current_sum_x = t;
        // Kahan sum for sum_y
        y = val2_in - c_sum_y;
        t = current_sum_y + y;
        c_sum_y = (t - current_sum_y) - y;
        current_sum_y = t;
        // Kahan sum for sum_x_sq
        y = val1_in_sq - c_sum_x_sq;
        t = current_sum_x_sq + y;
        c_sum_x_sq = (t - current_sum_x_sq) - y;
        current_sum_x_sq = t;
        // Kahan sum for sum_y_sq
        y = val2_in_sq - c_sum_y_sq;
        t = current_sum_y_sq + y;
        c_sum_y_sq = (t - current_sum_y_sq) - y;
        current_sum_y_sq = t;
        // Kahan sum for sum_xy
        y = val_xy_in - c_sum_xy;
        t = current_sum_xy + y;
        c_sum_xy = (t - current_sum_xy) - y;
        current_sum_xy = t;
      }

      // Subtract outgoing pair
      if (i >= n_effective) {
        double val1_out = data1(i - n_effective, j);
        double val2_out = data2(i - n_effective, j);
        if (!std::isnan(val1_out) && !std::isnan(val2_out)) {
          current_valid_pairs_count--;
          double val1_out_sq = val1_out * val1_out;
          double val2_out_sq = val2_out * val2_out;
          double val_xy_out = val1_out * val2_out;

          // Kahan sum for sum_x (subtracting)
          double y = -val1_out - c_sum_x;
          double t = current_sum_x + y;
          c_sum_x = (t - current_sum_x) - y;
          current_sum_x = t;
          // Kahan sum for sum_y (subtracting)
          y = -val2_out - c_sum_y;
          t = current_sum_y + y;
          c_sum_y = (t - current_sum_y) - y;
          current_sum_y = t;
          // Kahan sum for sum_x_sq (subtracting)
          y = -val1_out_sq - c_sum_x_sq;
          t = current_sum_x_sq + y;
          c_sum_x_sq = (t - current_sum_x_sq) - y;
          current_sum_x_sq = t;
          // Kahan sum for sum_y_sq (subtracting)
          y = -val2_out_sq - c_sum_y_sq;
          t = current_sum_y_sq + y;
          c_sum_y_sq = (t - current_sum_y_sq) - y;
          current_sum_y_sq = t;
          // Kahan sum for sum_xy (subtracting)
          y = -val_xy_out - c_sum_xy;
          t = current_sum_xy + y;
          c_sum_xy = (t - current_sum_xy) - y;
          current_sum_xy = t;
        }
      }

      if (current_valid_pairs_count >= min_periods) {
        double N_double = static_cast<double>(current_valid_pairs_count);
        double mean_x = current_sum_x / N_double;
        double mean_y = current_sum_y / N_double;
        double mean_xy = current_sum_xy / N_double;
        double mean_x_sq = current_sum_x_sq / N_double;
        double mean_y_sq = current_sum_y_sq / N_double;

        double var_x_pop = mean_x_sq - mean_x * mean_x;
        double var_y_pop = mean_y_sq - mean_y * mean_y;

        if (var_x_pop < 0 && var_x_pop > -1e-12)
          var_x_pop = 0;
        if (var_y_pop < 0 && var_y_pop > -1e-12)
          var_y_pop = 0;

        if (var_x_pop <= 1e-14 || var_y_pop <= 1e-14) {
          result(i, j) = NaN;
        } else {
          double std_x_pop = std::sqrt(var_x_pop);
          double std_y_pop = std::sqrt(var_y_pop);
          double cov_pop =
              mean_xy - mean_x * mean_y; // This is also used by ts_Cov
          result(i, j) = cov_pop / (std_x_pop * std_y_pop);
        }
      } else {
        result(i, j) = NaN;
      }
    }
  }
  return result;
}

DataFrame ts_Cov(const DataFrame &data1, const DataFrame &data2, int n_param) {
  if (data1.rows() != data2.rows() || data1.cols() != data2.cols()) {
    throw std::invalid_argument(
        "ts_Cov: data1 and data2 must have the same dimensions.");
  }

  int n_effective = (n_param <= 1) ? 2 : n_param;
  int min_periods = 2;

  DataFrame result = DataFrame::Constant(data1.rows(), data1.cols(), NaN);

  for (Eigen::Index j = 0; j < data1.cols(); ++j) {
    double current_sum_x = 0.0, c_sum_x = 0.0;
    double current_sum_y = 0.0, c_sum_y = 0.0;
    double current_sum_xy = 0.0, c_sum_xy = 0.0;
    int current_valid_pairs_count = 0;

    for (Eigen::Index i = 0; i < data1.rows(); ++i) {
      // Add incoming pair
      double val1_in = data1(i, j);
      double val2_in = data2(i, j);
      if (!std::isnan(val1_in) && !std::isnan(val2_in)) {
        current_valid_pairs_count++;
        double val_xy_in = val1_in * val2_in;

        // Kahan sum for sum_x
        double y = val1_in - c_sum_x;
        double t = current_sum_x + y;
        c_sum_x = (t - current_sum_x) - y;
        current_sum_x = t;
        // Kahan sum for sum_y
        y = val2_in - c_sum_y;
        t = current_sum_y + y;
        c_sum_y = (t - current_sum_y) - y;
        current_sum_y = t;
        // Kahan sum for sum_xy
        y = val_xy_in - c_sum_xy;
        t = current_sum_xy + y;
        c_sum_xy = (t - current_sum_xy) - y;
        current_sum_xy = t;
      }

      // Subtract outgoing pair
      if (i >= n_effective) {
        double val1_out = data1(i - n_effective, j);
        double val2_out = data2(i - n_effective, j);
        if (!std::isnan(val1_out) && !std::isnan(val2_out)) {
          current_valid_pairs_count--;
          double val_xy_out = val1_out * val2_out;

          // Kahan sum for sum_x (subtracting)
          double y = -val1_out - c_sum_x;
          double t = current_sum_x + y;
          c_sum_x = (t - current_sum_x) - y;
          current_sum_x = t;
          // Kahan sum for sum_y (subtracting)
          y = -val2_out - c_sum_y;
          t = current_sum_y + y;
          c_sum_y = (t - current_sum_y) - y;
          current_sum_y = t;
          // Kahan sum for sum_xy (subtracting)
          y = -val_xy_out - c_sum_xy;
          t = current_sum_xy + y;
          c_sum_xy = (t - current_sum_xy) - y;
          current_sum_xy = t;
        }
      }

      if (current_valid_pairs_count >= min_periods) {
        double N_double = static_cast<double>(current_valid_pairs_count);
        double mean_x = current_sum_x / N_double;
        double mean_y = current_sum_y / N_double;
        double mean_xy = current_sum_xy / N_double;
        result(i, j) = mean_xy - mean_x * mean_y; // Population covariance
      } else {
        result(i, j) = NaN;
      }
    }
  }
  return result;
}

DataFrame ts_Scale(const DataFrame &data, int n_param) {
  if (n_param <= 1) {
    return DataFrame::Constant(data.rows(), data.cols(), NaN);
  }
  int n_effective = n_param;
  // min_periods for min/max is 1 by default in their implementations.
  // For scaling, we need min and max to be valid and preferably different.

  DataFrame result = DataFrame::Constant(data.rows(), data.cols(), NaN);

  for (Eigen::Index j = 0; j < data.cols(); ++j) { // Iterate over columns
    for (Eigen::Index i = 0; i < data.rows();
         ++i) { // Iterate over rows (time points)
      double current_value = data(i, j);
      if (std::isnan(current_value)) {
        continue; // result(i, j) remains NaN
      }

      Eigen::Index start_row =
          std::max(static_cast<Eigen::Index>(0), i - n_effective + 1);
      Eigen::ArrayXd window_segment =
          data.col(j).segment(start_row, i - start_row + 1);

      double window_min = NaN;
      double window_max = NaN;
      int count_non_nan = 0;

      for (Eigen::Index k = 0; k < window_segment.size(); ++k) {
        if (!std::isnan(window_segment(k))) {
          if (count_non_nan == 0) {
            window_min = window_segment(k);
            window_max = window_segment(k);
          } else {
            if (window_segment(k) < window_min)
              window_min = window_segment(k);
            if (window_segment(k) > window_max)
              window_max = window_segment(k);
          }
          count_non_nan++;
        }
      }

      if (count_non_nan == 0 || std::isnan(window_min) ||
          std::isnan(window_max)) {
        // Not enough data, or min/max are NaN (should imply count_non_nan == 0)
        // result(i,j) remains NaN
        continue;
      }

      double range = window_max - window_min;

      if (std::abs(range) < 1e-14) { // Range is effectively zero
        // If range is zero, Python gives NaN for (val - min) / 0.
        // If val == min == max, then (x-x)/(x-x) = 0/0 -> NaN
        // If val != min but min==max, then (y-x)/0. This case should not happen
        // if min/max are calculated correctly. The only case for range ~ 0 is
        // if all non-NaN values in window are (almost) identical. In this case,
        // current_value should be equal to window_min. So (current_value -
        // window_min) is ~0. Result 0.0 / 0.0 -> NaN. Pandas behavior for x/0
        // where x is 0 is NaN. If all elements in window are same,
        // (val-val)/(val-val) -> NaN If only one element in window,
        // (val-val)/(val-val) -> NaN (already handled by n_param <=1 check)
        result(i, j) = NaN;
      } else {
        result(i, j) = (current_value - window_min) / range;
      }
    }
  }
  return result;
}

DataFrame ts_Skewness(const DataFrame &data, int n_param) {
  int N_rows = data.rows();
  int N_cols = data.cols();

  // Custom pandas-like handling of n_param (window size)
  int window_size = std::max(3, n_param); // Ensure n is at least 3 for skewness
  int min_periods = 1; // Match Python implementation: min_periods=1

  // The output will have the same number of rows as the input DataFrame
  DataFrame output = DataFrame::Constant(N_rows, N_cols, NaN);

  // Create start and end arrays for rolling window bounds
  std::vector<int64_t> start(N_rows), end(N_rows);
  for (int i = 0; i < N_rows; ++i) {
    start[i] = std::max(0, i - window_size + 1);
    end[i] = i + 1;
  }

  // Check if bounds are monotonic increasing for optimization
  bool is_monotonic_increasing_bounds =
      rolling::is_monotonic_increasing_start_end_bounds(start, end);

  // Process each column independently
  for (int col = 0; col < N_cols; ++col) {
    Eigen::ArrayXd values_copy = data.col(col);

    // --- Mean adjustment for numerical stability (from pandas code) ---
    double sum_val_for_mean = 0.0;
    int64_t nobs_for_mean = 0;
    double min_val_for_mean_adj = std::numeric_limits<double>::max();

    for (int i = 0; i < N_rows; ++i) {
      double val = values_copy(i);
      if (val == val) { // Not NaN
        nobs_for_mean++;
        sum_val_for_mean += val;
        if (val < min_val_for_mean_adj) {
          min_val_for_mean_adj = val;
        }
      }
    }

    double mean_val_for_adj = 0.0;
    if (nobs_for_mean > 0) {
      mean_val_for_adj = sum_val_for_mean / nobs_for_mean;
    }

    // Other cases would lead to imprecision for smallest values
    if (min_val_for_mean_adj - mean_val_for_adj > -1e5) {
      mean_val_for_adj = std::round(mean_val_for_adj);
      for (int i = 0; i < N_rows; ++i) {
        if (values_copy(i) == values_copy(i)) { // Not NaN
          values_copy(i) -= mean_val_for_adj;
        }
      }
    }
    // --- End of mean adjustment ---

    // Initialize state for the rolling calculation
    int64_t nobs = 0;
    double x = 0.0, xx = 0.0, xxx = 0.0;
    double compensation_x_add = 0.0, compensation_xx_add = 0.0,
           compensation_xxx_add = 0.0;
    double compensation_x_remove = 0.0, compensation_xx_remove = 0.0,
           compensation_xxx_remove = 0.0;
    int64_t num_consecutive_same_value = 0;
    double prev_value =
        NaN; // Initialize to NaN to ensure first value is different

    // Iterate through each window
    for (int i = 0; i < N_rows; ++i) {
      int64_t s = start[i];
      int64_t e = end[i];

      // Over the first window, or when bounds are not monotonic, or when
      // there's no overlap
      if (i == 0 || !is_monotonic_increasing_bounds || s >= end[i - 1]) {
        // Reset and recalculate from scratch
        prev_value = (s < N_rows) ? values_copy(s) : NaN;
        num_consecutive_same_value = 0;
        compensation_x_add = compensation_x_remove = 0.0;
        compensation_xx_add = compensation_xx_remove = 0.0;
        compensation_xxx_add = compensation_xxx_remove = 0.0;
        x = xx = xxx = 0.0;
        nobs = 0;

        for (int64_t j = s; j < e && j < N_rows; ++j) {
          rolling::add_skew(values_copy(j), nobs, x, xx, xxx,
                            compensation_x_add, compensation_xx_add,
                            compensation_xxx_add, num_consecutive_same_value,
                            prev_value);
        }
      } else {
        // Incremental calculation: remove old values and add new values

        // Calculate deletes
        for (int64_t j = start[i - 1]; j < s && j < N_rows; ++j) {
          rolling::remove_skew(values_copy(j), nobs, x, xx, xxx,
                               compensation_x_remove, compensation_xx_remove,
                               compensation_xxx_remove);
        }

        // Calculate adds
        for (int64_t j = end[i - 1]; j < e && j < N_rows; ++j) {
          rolling::add_skew(values_copy(j), nobs, x, xx, xxx,
                            compensation_x_add, compensation_xx_add,
                            compensation_xxx_add, num_consecutive_same_value,
                            prev_value);
        }
      }

      // Calculate skewness for the current window
      output(i, col) = rolling::calc_skew(min_periods, nobs, x, xx, xxx,
                                          num_consecutive_same_value);

      // Reset state if bounds are not monotonic
      if (!is_monotonic_increasing_bounds) {
        nobs = 0;
        x = xx = xxx = 0.0;
        compensation_x_remove = compensation_xx_remove =
            compensation_xxx_remove = 0.0;
      }
    }
  }
  return output;
}
// Helper function to check if a double value is NaN
bool is_nan_double(double val) { return val != val; }

// Corresponds to Cython's calc_kurt
double calc_kurt(int64_t minp, int64_t nobs, double x, double xx, double xxx,
                 double xxxx, int64_t num_consecutive_same_value) noexcept {
  double result;
  double dnobs;
  double A, B, C, D, R, K;

  if (nobs >= minp) { // This is the min_periods check for calc_kurt
    if (nobs < 4) {   // Kurtosis inherently needs at least 4 points
      result = NaN;
    } else if (num_consecutive_same_value >= nobs) {
      result = -3.0;
    } else {
      dnobs = static_cast<double>(nobs);
      A = x / dnobs;
      R = A * A;
      B = xx / dnobs - R;
      R = R * A;
      C = xxx / dnobs - R - 3 * A * B;
      R = R * A;
      D = xxxx / dnobs - R - 6 * B * A * A - 4 * C * A;

      if (B <= 1e-14) {
        result = NaN;
      } else {
        if (dnobs - 2.0 == 0 || dnobs - 3.0 == 0) {
          result = NaN;
        } else {
          K = (dnobs * dnobs - 1.0) * D / (B * B) -
              3.0 * std::pow((dnobs - 1.0), 2.0);
          result = K / ((dnobs - 2.0) * (dnobs - 3.0));
        }
      }
    }
  } else {
    result = NaN;
  }
  return result;
}

// Corresponds to Cython's add_kurt (using Kahan summation for precision)
void add_kurt(double val, int64_t &nobs, double &x, double &xx, double &xxx,
              double &xxxx, double &compensation_x, double &compensation_xx,
              double &compensation_xxx, double &compensation_xxxx,
              int64_t &num_consecutive_same_value,
              double &prev_value) noexcept {
  double y, t;

  if (!is_nan_double(val)) {
    nobs++;

    y = val - compensation_x;
    t = x + y;
    compensation_x = (t - x) - y;
    x = t;

    y = val * val - compensation_xx;
    t = xx + y;
    compensation_xx = (t - xx) - y;
    xx = t;

    y = val * val * val - compensation_xxx;
    t = xxx + y;
    compensation_xxx = (t - xxx) - y;
    xxx = t;

    y = val * val * val * val - compensation_xxxx;
    t = xxxx + y;
    compensation_xxxx = (t - xxxx) - y;
    xxxx = t;

    if (val == prev_value) {
      num_consecutive_same_value++;
    } else {
      num_consecutive_same_value = 1;
    }
    prev_value = val;
  }
}

// Corresponds to Cython's remove_kurt (using Kahan summation for precision)
void remove_kurt(double val, int64_t &nobs, double &x, double &xx, double &xxx,
                 double &xxxx, double &compensation_x, double &compensation_xx,
                 double &compensation_xxx, double &compensation_xxxx) noexcept {
  double y, t;

  if (!is_nan_double(val)) {
    nobs--;

    y = -val - compensation_x;
    t = x + y;
    compensation_x = (t - x) - y;
    x = t;

    y = -val * val - compensation_xx;
    t = xx + y;
    compensation_xx = (t - xx) - y;
    xx = t;

    y = -val * val * val - compensation_xxx;
    t = xxx + y;
    compensation_xxx = (t - xxx) - y;
    xxx = t;

    y = -val * val * val * val - compensation_xxxx;
    t = xxxx + y;
    compensation_xxxx = (t - xxxx) - y;
    xxxx = t;
  }
}

// The ts_Kurtosis function that mimics pandas' rolling behavior
DataFrame ts_Kurtosis(const DataFrame &s1, int n_param) {
  int N_rows = s1.rows();
  int N_cols = s1.cols();

  // Custom pandas-like handling of n_param (window size)
  int window_size = std::max(4, n_param); // Ensure n is at least 4 for kurtosis
  int min_periods = 1; // Match Python implementation: min_periods=1

  // The output will have the same number of rows as the input DataFrame
  DataFrame output = DataFrame::Constant(N_rows, N_cols, NaN);

  // Create start and end arrays for rolling window bounds
  std::vector<int64_t> start(N_rows), end(N_rows);
  for (int i = 0; i < N_rows; ++i) {
    start[i] = std::max(0, i - window_size + 1);
    end[i] = i + 1;
  }

  // Check if bounds are monotonic increasing for optimization
  bool is_monotonic_increasing_bounds =
      rolling::is_monotonic_increasing_start_end_bounds(start, end);

  // Process each column independently
  for (int col = 0; col < N_cols; ++col) {
    Eigen::ArrayXd values_copy = s1.col(col);

    // --- Mean adjustment for numerical stability (from original pandas code)
    // ---
    double sum_val_for_mean = 0.0;
    int64_t nobs_for_mean = 0;
    double min_val_for_mean_adj = std::numeric_limits<double>::max();

    for (int i = 0; i < N_rows; ++i) {
      double val = values_copy(i);
      if (!is_nan_double(val)) {
        nobs_for_mean++;
        sum_val_for_mean += val;
        if (val < min_val_for_mean_adj) {
          min_val_for_mean_adj = val;
        }
      }
    }

    double mean_val_for_adj = 0.0;
    if (nobs_for_mean > 0) {
      mean_val_for_adj = sum_val_for_mean / nobs_for_mean;
    }

    // Other cases would lead to imprecision for smallest values
    if (min_val_for_mean_adj - mean_val_for_adj > -1e4) {
      mean_val_for_adj = std::round(mean_val_for_adj);
      for (int i = 0; i < N_rows; ++i) {
        if (!is_nan_double(values_copy(i))) {
          values_copy(i) -= mean_val_for_adj;
        }
      }
    }
    // --- End of mean adjustment ---

    // Initialize state for the rolling calculation
    int64_t nobs = 0;
    double x = 0.0, xx = 0.0, xxx = 0.0, xxxx = 0.0;
    double compensation_x_add = 0.0, compensation_xx_add = 0.0,
           compensation_xxx_add = 0.0, compensation_xxxx_add = 0.0;
    double compensation_x_remove = 0.0, compensation_xx_remove = 0.0,
           compensation_xxx_remove = 0.0, compensation_xxxx_remove = 0.0;
    int64_t num_consecutive_same_value = 0;
    double prev_value =
        NaN; // Initialize to NaN to ensure first value is different

    // Iterate through each window
    for (int i = 0; i < N_rows; ++i) {
      int64_t s = start[i];
      int64_t e = end[i];

      // Over the first window, or when bounds are not monotonic, or when
      // there's no overlap
      if (i == 0 || !is_monotonic_increasing_bounds || s >= end[i - 1]) {
        // Reset and recalculate from scratch
        prev_value = (s < N_rows) ? values_copy(s) : NaN;
        num_consecutive_same_value = 0;
        compensation_x_add = compensation_x_remove = 0.0;
        compensation_xx_add = compensation_xx_remove = 0.0;
        compensation_xxx_add = compensation_xxx_remove = 0.0;
        compensation_xxxx_add = compensation_xxxx_remove = 0.0;
        x = xx = xxx = xxxx = 0.0;
        nobs = 0;

        for (int64_t j = s; j < e && j < N_rows; ++j) {
          rolling::add_kurt(values_copy(j), nobs, x, xx, xxx, xxxx,
                            compensation_x_add, compensation_xx_add,
                            compensation_xxx_add, compensation_xxxx_add,
                            num_consecutive_same_value, prev_value);
        }
      } else {
        // Incremental calculation: remove old values and add new values

        // Calculate deletes
        for (int64_t j = start[i - 1]; j < s && j < N_rows; ++j) {
          rolling::remove_kurt(values_copy(j), nobs, x, xx, xxx, xxxx,
                               compensation_x_remove, compensation_xx_remove,
                               compensation_xxx_remove,
                               compensation_xxxx_remove);
        }

        // Calculate adds
        for (int64_t j = end[i - 1]; j < e && j < N_rows; ++j) {
          rolling::add_kurt(values_copy(j), nobs, x, xx, xxx, xxxx,
                            compensation_x_add, compensation_xx_add,
                            compensation_xxx_add, compensation_xxxx_add,
                            num_consecutive_same_value, prev_value);
        }
      }

      // Calculate kurtosis for the current window
      output(i, col) = rolling::calc_kurt(min_periods, nobs, x, xx, xxx, xxxx,
                                          num_consecutive_same_value);

      // Reset state if bounds are not monotonic
      if (!is_monotonic_increasing_bounds) {
        nobs = 0;
        x = xx = xxx = xxxx = 0.0;
        compensation_x_remove = compensation_xx_remove =
            compensation_xxx_remove = compensation_xxxx_remove = 0.0;
      }
    }
  }
  return output;
}

DataFrame ts_Delta(const DataFrame &data, int n) {
  if (n < 0) {
    throw std::invalid_argument("ts_Delta: n must be non-negative.");
  }
  if (n == 0) {
    return DataFrame::Zero(data.rows(), data.cols());
  }

  DataFrame result =
      DataFrame::Constant(data.rows(), data.cols(), NAN); // 初始化为 NaN

  if (n >= data.rows()) {
    return result; // 如果延迟量 n 大于等于数据行数，则全部为 NaN
  }

  // 使用 Eigen 的切片操作进行高效计算
  // result 的有效行从第 n 行开始
  // 这些行等于 data 的当前行减去 data 的延迟 n 行
  result.bottomRows(data.rows() - n) =
      data.bottomRows(data.rows() - n) - data.topRows(data.rows() - n);

  return result;
}

DataFrame ts_Mean(const DataFrame &data, int n_param) {
  if (n_param < 0) {
    throw std::invalid_argument("ts_Mean: window size n must be non-negative.");
  }
  if (n_param == 0) { // Python: if n == 0: return s1
    return data;
  }

  int N_rows = data.rows();
  int N_cols = data.cols();
  int window_size = n_param;
  int min_periods = 1; // Match Python implementation: min_periods=1

  // The output will have the same number of rows as the input DataFrame
  DataFrame output = DataFrame::Constant(N_rows, N_cols, NaN);

  // Create start and end arrays for rolling window bounds
  std::vector<int64_t> start(N_rows), end(N_rows);
  for (int i = 0; i < N_rows; ++i) {
    start[i] = std::max(0, i - window_size + 1);
    end[i] = i + 1;
  }

  // Check if bounds are monotonic increasing for optimization
  bool is_monotonic_increasing_bounds =
      rolling::is_monotonic_increasing_start_end_bounds(start, end);

  // Process each column independently
  for (int col = 0; col < N_cols; ++col) {
    // Initialize state for the rolling calculation
    int64_t nobs = 0, neg_ct = 0;
    double sum_x = 0.0;
    double compensation_add = 0.0, compensation_remove = 0.0;
    int64_t num_consecutive_same_value = 0;
    double prev_value =
        NaN; // Initialize to NaN to ensure first value is different

    // Iterate through each window
    for (int i = 0; i < N_rows; ++i) {
      int64_t s = start[i];
      int64_t e = end[i];

      // Over the first window, or when bounds are not monotonic, or when
      // there's no overlap
      if (i == 0 || !is_monotonic_increasing_bounds || s >= end[i - 1]) {
        // Reset and recalculate from scratch
        compensation_add = compensation_remove = 0.0;
        sum_x = 0.0;
        nobs = neg_ct = 0;
        prev_value = (s < N_rows) ? data(s, col) : NaN;
        num_consecutive_same_value = 0;

        for (int64_t j = s; j < e && j < N_rows; ++j) {
          rolling::add_mean(data(j, col), nobs, sum_x, neg_ct, compensation_add,
                            num_consecutive_same_value, prev_value);
        }
      } else {
        // Incremental calculation: remove old values and add new values

        // Calculate deletes
        for (int64_t j = start[i - 1]; j < s && j < N_rows; ++j) {
          rolling::remove_mean(data(j, col), nobs, sum_x, neg_ct,
                               compensation_remove);
        }

        // Calculate adds
        for (int64_t j = end[i - 1]; j < e && j < N_rows; ++j) {
          rolling::add_mean(data(j, col), nobs, sum_x, neg_ct, compensation_add,
                            num_consecutive_same_value, prev_value);
        }
      }

      // Calculate mean for the current window
      output(i, col) =
          rolling::calc_mean(min_periods, nobs, neg_ct, sum_x,
                             num_consecutive_same_value, prev_value);

      // Reset state if bounds are not monotonic
      if (!is_monotonic_increasing_bounds) {
        nobs = neg_ct = 0;
        sum_x = 0.0;
        compensation_remove = 0.0;
      }
    }
  }
  return output;
}

DataFrame ts_Stdev(const DataFrame &data, int n_param) {
  if (n_param < 0) {
    throw std::invalid_argument(
        "ts_Stdev: window size n must be non-negative.");
  }
  if (n_param == 0) {
    return DataFrame::Constant(data.rows(), data.cols(), NaN);
  }

  int N_rows = data.rows();
  int N_cols = data.cols();
  int window_size = (n_param == 1) ? 2 : n_param; // Python: if n <= 1: n = 2
  int min_periods = 1; // Match Python implementation: min_periods=1
  int ddof = 1;        // Degrees of freedom for sample standard deviation

  // The output will have the same number of rows as the input DataFrame
  DataFrame output = DataFrame::Constant(N_rows, N_cols, NaN);

  // Create start and end arrays for rolling window bounds
  std::vector<int64_t> start(N_rows), end(N_rows);
  for (int i = 0; i < N_rows; ++i) {
    start[i] = std::max(0, i - window_size + 1);
    end[i] = i + 1;
  }

  // Check if bounds are monotonic increasing for optimization
  bool is_monotonic_increasing_bounds =
      rolling::is_monotonic_increasing_start_end_bounds(start, end);

  // Process each column independently
  for (int col = 0; col < N_cols; ++col) {
    // Initialize state for the rolling calculation
    double nobs = 0.0, mean_x = 0.0, ssqdm_x = 0.0;
    double compensation_add = 0.0, compensation_remove = 0.0;
    int64_t num_consecutive_same_value = 0;
    double prev_value =
        NaN; // Initialize to NaN to ensure first value is different

    // Iterate through each window
    for (int i = 0; i < N_rows; ++i) {
      int64_t s = start[i];
      int64_t e = end[i];

      // Over the first window, or when bounds are not monotonic, or when
      // there's no overlap
      if (i == 0 || !is_monotonic_increasing_bounds || s >= end[i - 1]) {
        // Reset and recalculate from scratch
        prev_value = (s < N_rows) ? data(s, col) : NaN;
        num_consecutive_same_value = 0;
        mean_x = ssqdm_x = nobs = compensation_add = compensation_remove = 0;

        for (int64_t j = s; j < e && j < N_rows; ++j) {
          rolling::add_var(data(j, col), nobs, mean_x, ssqdm_x,
                           compensation_add, num_consecutive_same_value,
                           prev_value);
        }
      } else {
        // Incremental calculation: remove old values and add new values

        // Calculate deletes
        for (int64_t j = start[i - 1]; j < s && j < N_rows; ++j) {
          rolling::remove_var(data(j, col), nobs, mean_x, ssqdm_x,
                              compensation_remove);
        }

        // Calculate adds
        for (int64_t j = end[i - 1]; j < e && j < N_rows; ++j) {
          rolling::add_var(data(j, col), nobs, mean_x, ssqdm_x,
                           compensation_add, num_consecutive_same_value,
                           prev_value);
        }
      }

      // Calculate variance for the current window
      double variance = rolling::calc_var(min_periods, ddof, nobs, ssqdm_x,
                                          num_consecutive_same_value);

      // Convert variance to standard deviation
      if (!std::isnan(variance) && variance >= 0) {
        output(i, col) = std::sqrt(variance);
      } else {
        output(i, col) = variance; // NaN or negative (should not happen)
      }

      // Reset state if bounds are not monotonic
      if (!is_monotonic_increasing_bounds) {
        nobs = 0.0;
        mean_x = 0.0;
        ssqdm_x = 0.0;
        compensation_remove = 0.0;
      }
    }
  }
  return output;
}

DataFrame ts_Min(const DataFrame &data, int n_param) {
  if (n_param < 0) {
    throw std::invalid_argument("ts_Min: window size n must be non-negative.");
  }
  if (n_param == 0) { // Python: if n == 0: return s1
    return data;
  }

  int N_rows = data.rows();
  int N_cols = data.cols();
  int window_size = n_param; // Python: no minimum window size for min/max
  int min_periods = 1;       // Match Python implementation: min_periods=1

  // The output will have the same number of rows as the input DataFrame
  DataFrame output = DataFrame::Constant(N_rows, N_cols, NaN);

  // Create start and end arrays for rolling window bounds
  std::vector<int64_t> start(N_rows), end(N_rows);
  for (int i = 0; i < N_rows; ++i) {
    start[i] = std::max(0, i - window_size + 1);
    end[i] = i + 1;
  }

  // Process each column independently
  for (int col = 0; col < N_cols; ++col) {
    // Initialize state for the rolling calculation
    int64_t nobs = 0;
    double min_val = std::numeric_limits<double>::infinity();

    // Iterate through each window
    for (int i = 0; i < N_rows; ++i) {
      int64_t s = start[i];
      int64_t e = end[i];

      // For min/max, we always recalculate from scratch since incremental
      // removal is complex (removing the min/max value requires finding new
      // min/max)
      nobs = 0;
      min_val = std::numeric_limits<double>::infinity();

      for (int64_t j = s; j < e && j < N_rows; ++j) {
        rolling::add_min(data(j, col), nobs, min_val);
      }

      // Calculate min for the current window
      output(i, col) = rolling::calc_min(min_periods, nobs, min_val);
    }
  }
  return output;
}

DataFrame ts_Max(const DataFrame &data, int n_param) {
  if (n_param < 0) {
    throw std::invalid_argument("ts_Max: window size n must be non-negative.");
  }
  if (n_param == 0) { // Python: if n == 0: return s1
    return data;
  }

  int N_rows = data.rows();
  int N_cols = data.cols();
  int window_size = n_param; // Python: no minimum window size for min/max
  int min_periods = 1;       // Match Python implementation: min_periods=1

  // The output will have the same number of rows as the input DataFrame
  DataFrame output = DataFrame::Constant(N_rows, N_cols, NaN);

  // Create start and end arrays for rolling window bounds
  std::vector<int64_t> start(N_rows), end(N_rows);
  for (int i = 0; i < N_rows; ++i) {
    start[i] = std::max(0, i - window_size + 1);
    end[i] = i + 1;
  }

  // Process each column independently
  for (int col = 0; col < N_cols; ++col) {
    // Initialize state for the rolling calculation
    int64_t nobs = 0;
    double max_val = -std::numeric_limits<double>::infinity();

    // Iterate through each window
    for (int i = 0; i < N_rows; ++i) {
      int64_t s = start[i];
      int64_t e = end[i];

      // For min/max, we always recalculate from scratch since incremental
      // removal is complex (removing the min/max value requires finding new
      // min/max)
      nobs = 0;
      max_val = -std::numeric_limits<double>::infinity();

      for (int64_t j = s; j < e && j < N_rows; ++j) {
        rolling::add_max(data(j, col), nobs, max_val);
      }

      // Calculate max for the current window
      output(i, col) = rolling::calc_max(min_periods, nobs, max_val);
    }
  }
  return output;
}

DataFrame ts_MeanChg(const DataFrame &data, int n_param) {
  int n_effective = std::max(3, n_param);

  // These functions should handle n_effective appropriately and return
  // DataFrames of the same dimensions as input 'data', with NaNs in initial
  // rows if applicable.
  DataFrame mean_values = ts_Mean(data, n_effective); // Uses renamed ts_Mean
  DataFrame decay_values =
      ts_Decay(data, n_effective); // ts_Decay name is unchanged

  // Element-wise subtraction. Eigen handles this directly.
  // If either mean_values(i,j) or decay_values(i,j) is NaN, result(i,j) will be
  // NaN.
  return mean_values - decay_values;
}

// Tot_ series wrapper functions (fixed window of 15)
DataFrame Tot_Mean(const DataFrame &data) {
  return ts_Mean(data, 15); // Uses the refactored ts_Mean
}
DataFrame Tot_Sum(const DataFrame &data) { return ts_Sum(data, 15); }
DataFrame Tot_Stdev(const DataFrame &data) { return ts_Stdev(data, 15); }
DataFrame Tot_Delta(const DataFrame &data) { return ts_Delta(data, 15); }
DataFrame Tot_Divide(const DataFrame &data) { return ts_Divide(data, 15); }
DataFrame Tot_ChgRate(const DataFrame &data) { return ts_ChgRate(data, 15); }
DataFrame Tot_Rank(const DataFrame &data) { return ts_Rank(data, 15); }
DataFrame Tot_Min(const DataFrame &data) { return ts_Min(data, 15); }
DataFrame Tot_Max(const DataFrame &data) { return ts_Max(data, 15); }
DataFrame Tot_ArgMax(const DataFrame &data) { return ts_Argmax(data, 15); }
DataFrame Tot_ArgMin(const DataFrame &data) { return ts_Argmin(data, 15); }

} // namespace feature_operators
