#include "feature_operators/rolling_aggregations.hpp"
#include <cmath>
#include <iostream>

namespace feature_operators {
namespace rolling {

// ----------------------------------------------------------------------
// Monotonic bounds checking (from pandas algos.pyx)

std::tuple<bool, bool, bool> is_monotonic(const std::vector<int64_t> &arr,
                                          bool timelike) {
  size_t n = arr.size();
  bool is_monotonic_inc = true;
  bool is_monotonic_dec = true;
  bool is_unique = true;
  bool is_strict_monotonic = true;

  if (n == 1) {
    // For single value, check if it's NaN (represented as special value)
    // In our case, we don't have NaN for int64_t, so return true
    return std::make_tuple(true, true, true);
  } else if (n < 2) {
    return std::make_tuple(true, true, true);
  }

  int64_t prev = arr[0];
  for (size_t i = 1; i < n; ++i) {
    int64_t cur = arr[i];

    if (cur < prev) {
      is_monotonic_inc = false;
    } else if (cur > prev) {
      is_monotonic_dec = false;
    } else if (cur == prev) {
      is_unique = false;
    }

    if (!is_monotonic_inc && !is_monotonic_dec) {
      break;
    }
    prev = cur;
  }

  is_strict_monotonic = is_unique && (is_monotonic_inc || is_monotonic_dec);
  return std::make_tuple(is_monotonic_inc, is_monotonic_dec,
                         is_strict_monotonic);
}

bool is_monotonic_increasing_start_end_bounds(const std::vector<int64_t> &start,
                                              const std::vector<int64_t> &end) {
  auto [start_inc, start_dec, start_strict] = is_monotonic(start, false);
  auto [end_inc, end_dec, end_strict] = is_monotonic(end, false);
  return start_inc && end_inc;
}

// ----------------------------------------------------------------------
// Rolling mean

double calc_mean(int64_t minp, int64_t nobs, int64_t neg_ct, double sum_x,
                 int64_t num_consecutive_same_value,
                 double prev_value) noexcept {
  double result;

  if (nobs >= minp && nobs > 0) {
    result = sum_x / static_cast<double>(nobs);
    if (num_consecutive_same_value >= nobs) {
      result = prev_value;
    } else if (neg_ct == 0 && result < 0) {
      // all positive
      result = 0;
    } else if (neg_ct == nobs && result > 0) {
      // all negative
      result = 0;
    }
  } else {
    result = NaN;
  }
  return result;
}

void add_mean(double val, int64_t &nobs, double &sum_x, int64_t &neg_ct,
              double &compensation, int64_t &num_consecutive_same_value,
              double &prev_value) noexcept {
  double y, t;

  // Not NaN
  if (val == val) {
    nobs = nobs + 1;
    y = val - compensation;
    t = sum_x + y;
    compensation = t - sum_x - y;
    sum_x = t;
    if (std::signbit(val)) {
      neg_ct = neg_ct + 1;
    }

    // GH#42064, record num of same values to remove floating point artifacts
    if (val == prev_value) {
      num_consecutive_same_value += 1;
    } else {
      // reset to 1 (include current value itself)
      num_consecutive_same_value = 1;
    }
    prev_value = val;
  }
}

void remove_mean(double val, int64_t &nobs, double &sum_x, int64_t &neg_ct,
                 double &compensation) noexcept {
  double y, t;

  if (val == val) {
    nobs = nobs - 1;
    y = -val - compensation;
    t = sum_x + y;
    compensation = t - sum_x - y;
    sum_x = t;
    if (std::signbit(val)) {
      neg_ct = neg_ct - 1;
    }
  }
}

// ----------------------------------------------------------------------
// Rolling variance

double calc_var(int64_t minp, int ddof, double nobs, double ssqdm_x,
                int64_t num_consecutive_same_value) noexcept {
  double result;

  // Variance is unchanged if no observation is added or removed
  if ((nobs >= minp) && (nobs > ddof)) {
    // pathological case & repeatedly same values case
    if (nobs == 1 || num_consecutive_same_value >= nobs) {
      result = 0;
    } else {
      result = ssqdm_x / (nobs - static_cast<double>(ddof));
    }
  } else {
    result = NaN;
  }

  return result;
}

void add_var(double val, double &nobs, double &mean_x, double &ssqdm_x,
             double &compensation, int64_t &num_consecutive_same_value,
             double &prev_value) noexcept {
  double delta, prev_mean, y, t;

  // Not NaN
  if (val == val) {
    nobs = nobs + 1;

    // GH#42064, record num of same values to remove floating point artifacts
    if (val == prev_value) {
      num_consecutive_same_value += 1;
    } else {
      // reset to 1 (include current value itself)
      num_consecutive_same_value = 1;
    }
    prev_value = val;

    // Welford's method for the online variance-calculation
    // using Kahan summation
    // https://en.wikipedia.org/wiki/Algorithms_for_calculating_variance
    prev_mean = mean_x - compensation;
    y = val - compensation;
    t = y - mean_x;
    compensation = t + mean_x - y;
    delta = t;
    if (nobs) {
      mean_x = mean_x + delta / nobs;
    } else {
      mean_x = 0;
    }
    ssqdm_x = ssqdm_x + (val - prev_mean) * (val - mean_x);
  }
}

void remove_var(double val, double &nobs, double &mean_x, double &ssqdm_x,
                double &compensation) noexcept {
  double delta, prev_mean, y, t;
  if (val == val) {
    nobs = nobs - 1;
    if (nobs) {
      // Welford's method for the online variance-calculation
      // using Kahan summation
      // https://en.wikipedia.org/wiki/Algorithms_for_calculating_variance
      prev_mean = mean_x - compensation;
      y = val - compensation;
      t = y - mean_x;
      compensation = t + mean_x - y;
      delta = t;
      mean_x = mean_x - delta / nobs;
      ssqdm_x = ssqdm_x - (val - prev_mean) * (val - mean_x);
    } else {
      mean_x = 0;
      ssqdm_x = 0;
    }
  }
}

// ----------------------------------------------------------------------
// Rolling skewness

double calc_skew(int64_t minp, int64_t nobs, double x, double xx, double xxx,
                 int64_t num_consecutive_same_value) noexcept {
  double result;
  double dnobs;
  double A, B, C, R;

  if (nobs >= minp) {
    dnobs = static_cast<double>(nobs);
    A = x / dnobs;
    B = xx / dnobs - A * A;
    C = xxx / dnobs - A * A * A - 3 * A * B;

    if (nobs < 3) {
      result = NaN;
    } else if (num_consecutive_same_value >= nobs) {
      // Uniform case, force result to be 0
      result = 0.0;
    } else if (B <= 1e-14) {
      // Variance is effectively zero, return NaN
      result = NaN;
    } else {
      R = std::sqrt(B);
      result =
          ((std::sqrt(dnobs * (dnobs - 1.0)) * C) / ((dnobs - 2) * R * R * R));
    }
  } else {
    result = NaN;
  }

  return result;
}

void add_skew(double val, int64_t &nobs, double &x, double &xx, double &xxx,
              double &compensation_x, double &compensation_xx,
              double &compensation_xxx, int64_t &num_consecutive_same_value,
              double &prev_value) noexcept {
  double y, t;

  // Not NaN
  if (val == val) {
    nobs = nobs + 1;

    y = val - compensation_x;
    t = x + y;
    compensation_x = t - x - y;
    x = t;

    y = val * val - compensation_xx;
    t = xx + y;
    compensation_xx = t - xx - y;
    xx = t;

    y = val * val * val - compensation_xxx;
    t = xxx + y;
    compensation_xxx = t - xxx - y;
    xxx = t;

    // Track consecutive same values for floating point artifacts
    if (val == prev_value) {
      num_consecutive_same_value += 1;
    } else {
      // Reset to 1 (include current value itself)
      num_consecutive_same_value = 1;
    }
    prev_value = val;
  }
}

void remove_skew(double val, int64_t &nobs, double &x, double &xx, double &xxx,
                 double &compensation_x, double &compensation_xx,
                 double &compensation_xxx) noexcept {
  double y, t;

  // Not NaN
  if (val == val) {
    nobs = nobs - 1;

    y = -val - compensation_x;
    t = x + y;
    compensation_x = t - x - y;
    x = t;

    y = -val * val - compensation_xx;
    t = xx + y;
    compensation_xx = t - xx - y;
    xx = t;

    y = -val * val * val - compensation_xxx;
    t = xxx + y;
    compensation_xxx = t - xxx - y;
    xxx = t;
  }
}

// ----------------------------------------------------------------------
// Rolling kurtosis

double calc_kurt(int64_t minp, int64_t nobs, double x, double xx, double xxx,
                 double xxxx, int64_t num_consecutive_same_value) noexcept {
  double result;
  double dnobs;
  double A, B, C, D, R, K;

  if (nobs >= minp) {

    if (nobs < 4) {
      result = NaN;
    } else if (num_consecutive_same_value >= nobs) {
      // Uniform case, force result to be 0
      result = -3.0;
    } else {
      dnobs = static_cast<double>(nobs);
      A = x / dnobs;
      R = A * A;
      B = xx / dnobs - R;
      R = R * A;
      C = xxx / dnobs - R - 3 * A * B;
      R = R * A;
      D = xxxx / dnobs - R - 6 * A * A * B - 4 * A * C;
      if (B <= 1e-14) {
        result = NaN;
      } else {
        K = (dnobs * dnobs - 1.0) * D / (B * B) -
            3.0 * std::pow((dnobs - 1.0), 2.0);
        result = K / ((dnobs - 2.) * (dnobs - 3.));
      }
    }
  } else {
    result = NaN;
  }
  return result;
}

void add_kurt(double val, int64_t &nobs, double &x, double &xx, double &xxx,
              double &xxxx, double &compensation_x, double &compensation_xx,
              double &compensation_xxx, double &compensation_xxxx,
              int64_t &num_consecutive_same_value,
              double &prev_value) noexcept {
  double y, t;

  // Not NaN
  if (val == val) {
    nobs = nobs + 1;

    y = val - compensation_x;
    t = x + y;
    compensation_x = t - x - y;
    x = t;

    y = val * val - compensation_xx;
    t = xx + y;
    compensation_xx = t - xx - y;
    xx = t;

    y = val * val * val - compensation_xxx;
    t = xxx + y;
    compensation_xxx = t - xxx - y;
    xxx = t;

    y = val * val * val * val - compensation_xxxx;
    t = xxxx + y;
    compensation_xxxx = t - xxxx - y;
    xxxx = t;

    // Track consecutive same values for floating point artifacts
    if (val == prev_value) {
      num_consecutive_same_value += 1;
    } else {
      // Reset to 1 (include current value itself)
      num_consecutive_same_value = 1;
    }
    prev_value = val;
  }
}

void remove_kurt(double val, int64_t &nobs, double &x, double &xx, double &xxx,
                 double &xxxx, double &compensation_x, double &compensation_xx,
                 double &compensation_xxx, double &compensation_xxxx) noexcept {
  double y, t;

  // Not NaN
  if (val == val) {
    nobs = nobs - 1;

    y = -val - compensation_x;
    t = x + y;
    compensation_x = t - x - y;
    x = t;

    y = -val * val - compensation_xx;
    t = xx + y;
    compensation_xx = t - xx - y;
    xx = t;

    y = -val * val * val - compensation_xxx;
    t = xxx + y;
    compensation_xxx = t - xxx - y;
    xxx = t;

    y = -val * val * val * val - compensation_xxxx;
    t = xxxx + y;
    compensation_xxxx = t - xxxx - y;
    xxxx = t;
  }
}

// ----------------------------------------------------------------------
// Rolling sum

double calc_sum(int64_t minp, int64_t nobs, double sum_x) noexcept {
  if (nobs >= minp && nobs > 0) {
    return sum_x;
  } else {
    return NaN;
  }
}

void add_sum(double val, int64_t &nobs, double &sum_x,
             double &compensation) noexcept {
  double y, t;

  // Not NaN
  if (val == val) {
    nobs = nobs + 1;
    y = val - compensation;
    t = sum_x + y;
    compensation = t - sum_x - y;
    sum_x = t;
  }
}

void remove_sum(double val, int64_t &nobs, double &sum_x,
                double &compensation) noexcept {
  double y, t;

  if (val == val) {
    nobs = nobs - 1;
    y = -val - compensation;
    t = sum_x + y;
    compensation = t - sum_x - y;
    sum_x = t;
  }
}

// ----------------------------------------------------------------------
// Rolling min/max

double calc_min(int64_t minp, int64_t nobs, double min_val) noexcept {
  if (nobs >= minp && nobs > 0) {
    return min_val;
  } else {
    return NaN;
  }
}

double calc_max(int64_t minp, int64_t nobs, double max_val) noexcept {
  if (nobs >= minp && nobs > 0) {
    return max_val;
  } else {
    return NaN;
  }
}

void add_min(double val, int64_t &nobs, double &min_val) noexcept {
  // Not NaN
  if (val == val) {
    if (nobs == 0 || val < min_val) {
      min_val = val;
    }
    nobs = nobs + 1;
  }
}

void add_max(double val, int64_t &nobs, double &max_val) noexcept {
  // Not NaN
  if (val == val) {
    if (nobs == 0 || val > max_val) {
      max_val = val;
    }
    nobs = nobs + 1;
  }
}

void remove_min(double val, int64_t &nobs, double &min_val) noexcept {
  // For min/max, removal requires recalculation of the entire window
  // This is just a placeholder - actual removal logic needs to be handled
  // by recalculating the entire window when bounds are not monotonic
  if (val == val) {
    nobs = nobs - 1;
  }
}

void remove_max(double val, int64_t &nobs, double &max_val) noexcept {
  // For min/max, removal requires recalculation of the entire window
  // This is just a placeholder - actual removal logic needs to be handled
  // by recalculating the entire window when bounds are not monotonic
  if (val == val) {
    nobs = nobs - 1;
  }
}

// ----------------------------------------------------------------------
// High-level rolling window functions (similar to pandas rolling API)

DataFrame roll_mean(const DataFrame &data, int window, int min_periods) {
  if (window <= 0) {
    throw std::invalid_argument("roll_mean: window size must be positive.");
  }

  int N_rows = data.rows();
  int N_cols = data.cols();
  DataFrame output = DataFrame::Constant(N_rows, N_cols, NaN);

  // Create start and end arrays for rolling window bounds
  std::vector<int64_t> start(N_rows), end(N_rows);
  for (int i = 0; i < N_rows; ++i) {
    start[i] = std::max(0, i - window + 1);
    end[i] = i + 1;
  }

  // Check if bounds are monotonic increasing for optimization
  bool is_monotonic_increasing_bounds =
      is_monotonic_increasing_start_end_bounds(start, end);

  // Process each column independently
  for (int col = 0; col < N_cols; ++col) {
    // Initialize state for the rolling calculation
    int64_t nobs = 0, neg_ct = 0;
    double sum_x = 0.0;
    double compensation_add = 0.0, compensation_remove = 0.0;
    int64_t num_consecutive_same_value = 0;
    double prev_value = NaN;

    // Iterate through each window
    for (int i = 0; i < N_rows; ++i) {
      int64_t s = start[i];
      int64_t e = end[i];

      // Over the first window, or when bounds are not monotonic, or when
      // there's no overlap
      if (i == 0 || !is_monotonic_increasing_bounds || s >= end[i - 1]) {
        // Reset and recalculate from scratch
        compensation_add = compensation_remove = 0.0;
        sum_x = 0.0;
        nobs = neg_ct = 0;
        prev_value = (s < N_rows) ? data(s, col) : NaN;
        num_consecutive_same_value = 0;

        for (int64_t j = s; j < e && j < N_rows; ++j) {
          add_mean(data(j, col), nobs, sum_x, neg_ct, compensation_add,
                   num_consecutive_same_value, prev_value);
        }
      } else {
        // Incremental calculation: remove old values and add new values

        // Calculate deletes
        for (int64_t j = start[i - 1]; j < s && j < N_rows; ++j) {
          remove_mean(data(j, col), nobs, sum_x, neg_ct, compensation_remove);
        }

        // Calculate adds
        for (int64_t j = end[i - 1]; j < e && j < N_rows; ++j) {
          add_mean(data(j, col), nobs, sum_x, neg_ct, compensation_add,
                   num_consecutive_same_value, prev_value);
        }
      }

      // Calculate mean for the current window
      output(i, col) = calc_mean(min_periods, nobs, neg_ct, sum_x,
                                 num_consecutive_same_value, prev_value);

      // Reset state if bounds are not monotonic
      if (!is_monotonic_increasing_bounds) {
        nobs = neg_ct = 0;
        sum_x = 0.0;
        compensation_remove = 0.0;
      }
    }
  }
  return output;
}

DataFrame roll_var(const DataFrame &data, int window, int min_periods,
                   int ddof) {
  if (window <= 0) {
    throw std::invalid_argument("roll_var: window size must be positive.");
  }

  int N_rows = data.rows();
  int N_cols = data.cols();
  DataFrame output = DataFrame::Constant(N_rows, N_cols, NaN);

  // Create start and end arrays for rolling window bounds
  std::vector<int64_t> start(N_rows), end(N_rows);
  for (int i = 0; i < N_rows; ++i) {
    start[i] = std::max(0, i - window + 1);
    end[i] = i + 1;
  }

  // Check if bounds are monotonic increasing for optimization
  bool is_monotonic_increasing_bounds =
      is_monotonic_increasing_start_end_bounds(start, end);

  // Process each column independently
  for (int col = 0; col < N_cols; ++col) {
    // Initialize state for the rolling calculation
    double nobs = 0.0, mean_x = 0.0, ssqdm_x = 0.0;
    double compensation_add = 0.0, compensation_remove = 0.0;
    int64_t num_consecutive_same_value = 0;
    double prev_value = NaN;

    // Iterate through each window
    for (int i = 0; i < N_rows; ++i) {
      int64_t s = start[i];
      int64_t e = end[i];

      // Over the first window, or when bounds are not monotonic, or when
      // there's no overlap
      if (i == 0 || !is_monotonic_increasing_bounds || s >= end[i - 1]) {
        // Reset and recalculate from scratch
        prev_value = (s < N_rows) ? data(s, col) : NaN;
        num_consecutive_same_value = 0;
        mean_x = ssqdm_x = nobs = compensation_add = compensation_remove = 0;

        for (int64_t j = s; j < e && j < N_rows; ++j) {
          add_var(data(j, col), nobs, mean_x, ssqdm_x, compensation_add,
                  num_consecutive_same_value, prev_value);
        }
      } else {
        // Incremental calculation: remove old values and add new values

        // Calculate deletes
        for (int64_t j = start[i - 1]; j < s && j < N_rows; ++j) {
          remove_var(data(j, col), nobs, mean_x, ssqdm_x, compensation_remove);
        }

        // Calculate adds
        for (int64_t j = end[i - 1]; j < e && j < N_rows; ++j) {
          add_var(data(j, col), nobs, mean_x, ssqdm_x, compensation_add,
                  num_consecutive_same_value, prev_value);
        }
      }

      // Calculate variance for the current window
      output(i, col) = calc_var(min_periods, ddof, nobs, ssqdm_x,
                                num_consecutive_same_value);

      // Reset state if bounds are not monotonic
      if (!is_monotonic_increasing_bounds) {
        nobs = 0.0;
        mean_x = 0.0;
        ssqdm_x = 0.0;
        compensation_remove = 0.0;
      }
    }
  }
  return output;
}

DataFrame roll_std(const DataFrame &data, int window, int min_periods,
                   int ddof) {
  DataFrame variance = roll_var(data, window, min_periods, ddof);

  // Convert variance to standard deviation
  for (int i = 0; i < variance.rows(); ++i) {
    for (int j = 0; j < variance.cols(); ++j) {
      if (!std::isnan(variance(i, j)) && variance(i, j) >= 0) {
        variance(i, j) = std::sqrt(variance(i, j));
      }
    }
  }
  return variance;
}

DataFrame roll_sum(const DataFrame &data, int window, int min_periods) {
  if (window <= 0) {
    throw std::invalid_argument("roll_sum: window size must be positive.");
  }

  int N_rows = data.rows();
  int N_cols = data.cols();
  DataFrame output = DataFrame::Constant(N_rows, N_cols, NaN);

  // Create start and end arrays for rolling window bounds
  std::vector<int64_t> start(N_rows), end(N_rows);
  for (int i = 0; i < N_rows; ++i) {
    start[i] = std::max(0, i - window + 1);
    end[i] = i + 1;
  }

  // Check if bounds are monotonic increasing for optimization
  bool is_monotonic_increasing_bounds =
      is_monotonic_increasing_start_end_bounds(start, end);

  // Process each column independently
  for (int col = 0; col < N_cols; ++col) {
    // Initialize state for the rolling calculation
    int64_t nobs = 0;
    double sum_x = 0.0;
    double compensation_add = 0.0, compensation_remove = 0.0;

    // Iterate through each window
    for (int i = 0; i < N_rows; ++i) {
      int64_t s = start[i];
      int64_t e = end[i];

      // Over the first window, or when bounds are not monotonic, or when
      // there's no overlap
      if (i == 0 || !is_monotonic_increasing_bounds || s >= end[i - 1]) {
        // Reset and recalculate from scratch
        compensation_add = compensation_remove = 0.0;
        sum_x = 0.0;
        nobs = 0;

        for (int64_t j = s; j < e && j < N_rows; ++j) {
          add_sum(data(j, col), nobs, sum_x, compensation_add);
        }
      } else {
        // Incremental calculation: remove old values and add new values

        // Calculate deletes
        for (int64_t j = start[i - 1]; j < s && j < N_rows; ++j) {
          remove_sum(data(j, col), nobs, sum_x, compensation_remove);
        }

        // Calculate adds
        for (int64_t j = end[i - 1]; j < e && j < N_rows; ++j) {
          add_sum(data(j, col), nobs, sum_x, compensation_add);
        }
      }

      // Calculate sum for the current window
      output(i, col) = calc_sum(min_periods, nobs, sum_x);

      // Reset state if bounds are not monotonic
      if (!is_monotonic_increasing_bounds) {
        nobs = 0;
        sum_x = 0.0;
        compensation_remove = 0.0;
      }
    }
  }
  return output;
}

DataFrame roll_min(const DataFrame &data, int window, int min_periods) {
  if (window <= 0) {
    throw std::invalid_argument("roll_min: window size must be positive.");
  }

  int N_rows = data.rows();
  int N_cols = data.cols();
  DataFrame output = DataFrame::Constant(N_rows, N_cols, NaN);

  // Create start and end arrays for rolling window bounds
  std::vector<int64_t> start(N_rows), end(N_rows);
  for (int i = 0; i < N_rows; ++i) {
    start[i] = std::max(0, i - window + 1);
    end[i] = i + 1;
  }

  // Process each column independently
  for (int col = 0; col < N_cols; ++col) {
    // Iterate through each window
    for (int i = 0; i < N_rows; ++i) {
      int64_t s = start[i];
      int64_t e = end[i];

      // For min/max, we always recalculate from scratch since incremental
      // removal is complex (removing the min/max value requires finding new
      // min/max)
      int64_t nobs = 0;
      double min_val = std::numeric_limits<double>::infinity();

      for (int64_t j = s; j < e && j < N_rows; ++j) {
        add_min(data(j, col), nobs, min_val);
      }

      // Calculate min for the current window
      output(i, col) = calc_min(min_periods, nobs, min_val);
    }
  }
  return output;
}

DataFrame roll_max(const DataFrame &data, int window, int min_periods) {
  if (window <= 0) {
    throw std::invalid_argument("roll_max: window size must be positive.");
  }

  int N_rows = data.rows();
  int N_cols = data.cols();
  DataFrame output = DataFrame::Constant(N_rows, N_cols, NaN);

  // Create start and end arrays for rolling window bounds
  std::vector<int64_t> start(N_rows), end(N_rows);
  for (int i = 0; i < N_rows; ++i) {
    start[i] = std::max(0, i - window + 1);
    end[i] = i + 1;
  }

  // Process each column independently
  for (int col = 0; col < N_cols; ++col) {
    // Iterate through each window
    for (int i = 0; i < N_rows; ++i) {
      int64_t s = start[i];
      int64_t e = end[i];

      // For min/max, we always recalculate from scratch since incremental
      // removal is complex (removing the min/max value requires finding new
      // min/max)
      int64_t nobs = 0;
      double max_val = -std::numeric_limits<double>::infinity();

      for (int64_t j = s; j < e && j < N_rows; ++j) {
        add_max(data(j, col), nobs, max_val);
      }

      // Calculate max for the current window
      output(i, col) = calc_max(min_periods, nobs, max_val);
    }
  }
  return output;
}

DataFrame roll_median(const DataFrame &data, int window, int min_periods) {
  if (window <= 0) {
    throw std::invalid_argument("roll_median: window size must be positive.");
  }

  int N_rows = data.rows();
  int N_cols = data.cols();
  DataFrame output = DataFrame::Constant(N_rows, N_cols, NaN);

  // Create start and end arrays for rolling window bounds
  std::vector<int64_t> start(N_rows), end(N_rows);
  for (int i = 0; i < N_rows; ++i) {
    start[i] = std::max(0, i - window + 1);
    end[i] = i + 1;
  }

  // Process each column independently
  for (int col = 0; col < N_cols; ++col) {
    // Iterate through each window
    for (int i = 0; i < N_rows; ++i) {
      int64_t s = start[i];
      int64_t e = end[i];

      // For median, we always recalculate from scratch since it requires
      // sorting
      std::vector<double> window_values;
      for (int64_t j = s; j < e && j < N_rows; ++j) {
        double val = data(j, col);
        if (!std::isnan(val)) {
          window_values.push_back(val);
        }
      }

      // Calculate median for the current window
      if (window_values.size() >= static_cast<size_t>(min_periods)) {
        std::sort(window_values.begin(), window_values.end());

        size_t size = window_values.size();
        if (size % 2 == 1) {
          // Odd number of elements, take the middle one
          output(i, col) = window_values[size / 2];
        } else {
          // Even number of elements, take the average of the two middle ones
          output(i, col) =
              (window_values[size / 2 - 1] + window_values[size / 2]) / 2.0;
        }
      } else {
        output(i, col) = NaN;
      }
    }
  }
  return output;
}

DataFrame roll_skew(const DataFrame &data, int window, int min_periods) {
  if (window <= 0) {
    throw std::invalid_argument("roll_skew: window size must be positive.");
  }

  int N_rows = data.rows();
  int N_cols = data.cols();
  DataFrame output = DataFrame::Constant(N_rows, N_cols, NaN);

  // Create start and end arrays for rolling window bounds
  std::vector<int64_t> start(N_rows), end(N_rows);
  for (int i = 0; i < N_rows; ++i) {
    start[i] = std::max(0, i - window + 1);
    end[i] = i + 1;
  }

  // Check if bounds are monotonic increasing for optimization
  bool is_monotonic_increasing_bounds =
      is_monotonic_increasing_start_end_bounds(start, end);

  // Process each column independently
  for (int col = 0; col < N_cols; ++col) {
    Eigen::ArrayXd values_copy = data.col(col);

    // --- Mean adjustment for numerical stability (from pandas code) ---
    double sum_val_for_mean = 0.0;
    int64_t nobs_for_mean = 0;
    double min_val_for_mean_adj = std::numeric_limits<double>::max();

    for (int i = 0; i < N_rows; ++i) {
      double val = values_copy(i);
      if (val == val) { // Not NaN
        nobs_for_mean++;
        sum_val_for_mean += val;
        if (val < min_val_for_mean_adj) {
          min_val_for_mean_adj = val;
        }
      }
    }

    double mean_val_for_adj = 0.0;
    if (nobs_for_mean > 0) {
      mean_val_for_adj = sum_val_for_mean / nobs_for_mean;
    }

    // Other cases would lead to imprecision for smallest values
    if (min_val_for_mean_adj - mean_val_for_adj > -1e5) {
      mean_val_for_adj = std::round(mean_val_for_adj);
      for (int i = 0; i < N_rows; ++i) {
        if (values_copy(i) == values_copy(i)) { // Not NaN
          values_copy(i) -= mean_val_for_adj;
        }
      }
    }
    // --- End of mean adjustment ---

    // Initialize state for the rolling calculation
    int64_t nobs = 0;
    double x = 0.0, xx = 0.0, xxx = 0.0;
    double compensation_x_add = 0.0, compensation_xx_add = 0.0,
           compensation_xxx_add = 0.0;
    double compensation_x_remove = 0.0, compensation_xx_remove = 0.0,
           compensation_xxx_remove = 0.0;
    int64_t num_consecutive_same_value = 0;
    double prev_value =
        NaN; // Initialize to NaN to ensure first value is different

    // Iterate through each window
    for (int i = 0; i < N_rows; ++i) {
      int64_t s = start[i];
      int64_t e = end[i];

      // Over the first window, or when bounds are not monotonic, or when
      // there's no overlap
      if (i == 0 || !is_monotonic_increasing_bounds || s >= end[i - 1]) {
        // Reset and recalculate from scratch
        prev_value = (s < N_rows) ? values_copy(s) : NaN;
        num_consecutive_same_value = 0;
        compensation_x_add = compensation_x_remove = 0.0;
        compensation_xx_add = compensation_xx_remove = 0.0;
        compensation_xxx_add = compensation_xxx_remove = 0.0;
        x = xx = xxx = 0.0;
        nobs = 0;

        for (int64_t j = s; j < e && j < N_rows; ++j) {
          rolling::add_skew(values_copy(j), nobs, x, xx, xxx,
                            compensation_x_add, compensation_xx_add,
                            compensation_xxx_add, num_consecutive_same_value,
                            prev_value);
        }
      } else {
        // Incremental calculation: remove old values and add new values

        // Calculate deletes
        for (int64_t j = start[i - 1]; j < s && j < N_rows; ++j) {
          rolling::remove_skew(values_copy(j), nobs, x, xx, xxx,
                               compensation_x_remove, compensation_xx_remove,
                               compensation_xxx_remove);
        }

        // Calculate adds
        for (int64_t j = end[i - 1]; j < e && j < N_rows; ++j) {
          rolling::add_skew(values_copy(j), nobs, x, xx, xxx,
                            compensation_x_add, compensation_xx_add,
                            compensation_xxx_add, num_consecutive_same_value,
                            prev_value);
        }
      }

      // Calculate skewness for the current window
      output(i, col) = rolling::calc_skew(min_periods, nobs, x, xx, xxx,
                                          num_consecutive_same_value);

      // Reset state if bounds are not monotonic
      if (!is_monotonic_increasing_bounds) {
        nobs = 0;
        x = xx = xxx = 0.0;
        compensation_x_remove = compensation_xx_remove =
            compensation_xxx_remove = 0.0;
      }
    }
  }
  return output;
}

DataFrame roll_kurt(const DataFrame &data, int window, int min_periods) {
  if (window <= 0) {
    throw std::invalid_argument("roll_kurt: window size must be positive.");
  }
  int N_rows = data.rows();
  int N_cols = data.cols();
  DataFrame output = DataFrame::Constant(N_rows, N_cols, NaN);

  // Create start and end arrays for rolling window bounds
  std::vector<int64_t> start(N_rows), end(N_rows);
  for (int i = 0; i < N_rows; ++i) {
    start[i] = std::max(0, i - window + 1);
    end[i] = i + 1;
  }
  int minp = std::max(min_periods, 4);
  // Check if bounds are monotonic increasing for optimization
  bool is_monotonic_increasing_bounds =
      is_monotonic_increasing_start_end_bounds(start, end);

  // Process each column independently
  for (int col = 0; col < N_cols; ++col) {
    Eigen::ArrayXd values_copy = data.col(col);
    // --- Mean adjustment for numerical stability (from pandas code) ---
    double sum_val_for_mean = 0.0;
    int64_t nobs_for_mean = 0;
    double min_val_for_mean_adj = std::numeric_limits<double>::max();

    for (int i = 0; i < N_rows; ++i) {
      double val = values_copy(i);
      if (val == val) { // Not NaN
        nobs_for_mean++;
        sum_val_for_mean += val;
        if (val < min_val_for_mean_adj) {
          min_val_for_mean_adj = val;
        }
      }
    }

    double mean_val_for_adj = sum_val_for_mean / nobs_for_mean;

    // Other cases would lead to imprecision for smallest values
    if (min_val_for_mean_adj - mean_val_for_adj > -1e4) {
      mean_val_for_adj = std::round(mean_val_for_adj);
      for (int i = 0; i < N_rows; ++i) {
        values_copy(i) -= mean_val_for_adj;
      }
    }
    // --- End of mean adjustment ---

    // Initialize state for the rolling calculation
    int64_t nobs = 0;
    double x = 0.0, xx = 0.0, xxx = 0.0, xxxx = 0.0;
    double compensation_x = 0.0, compensation_xx = 0.0, compensation_xxx = 0.0,
           compensation_xxxx = 0.0;
    double compensation_remove_x = 0.0, compensation_remove_xx = 0.0,
           compensation_remove_xxx = 0.0, compensation_remove_xxxx = 0.0;
    int64_t num_consecutive_same_value = 0;
    double prev_value = NaN;

    // Iterate through each window
    for (int i = 0; i < N_rows; ++i) {
      int64_t s = start[i];
      int64_t e = end[i];

      // Over the first window, or when bounds are not monotonic, or when
      // there's no overlap
      if (i == 0 || !is_monotonic_increasing_bounds || s >= end[i - 1]) {
        // Reset and recalculate from scratch
        prev_value = data(s, col);
        // prev_value = (s < N_rows) ? data(s, col) : NaN;
        num_consecutive_same_value = 0;
        x = xx = xxx = xxxx = 0.0;
        nobs = 0;
        compensation_x = compensation_xx = compensation_xxx =
            compensation_xxxx = 0.0;
        compensation_remove_x = compensation_remove_xx =
            compensation_remove_xxx = compensation_remove_xxxx = 0.0;

        for (int64_t j = s; j < e; ++j) {
          add_kurt(values_copy(j), nobs, x, xx, xxx, xxxx, compensation_x,
                   compensation_xx, compensation_xxx, compensation_xxxx,
                   num_consecutive_same_value, prev_value);
        }
      } else {
        // Incremental calculation: remove old values and add new values

        // Calculate deletes
        for (int64_t j = start[i - 1]; j < s; ++j) {
          remove_kurt(values_copy(j), nobs, x, xx, xxx, xxxx,
                      compensation_remove_x, compensation_remove_xx,
                      compensation_remove_xxx, compensation_remove_xxxx);
        }

        // Calculate adds
        for (int64_t j = end[i - 1]; j < e; ++j) {
          add_kurt(values_copy(j), nobs, x, xx, xxx, xxxx, compensation_x,
                   compensation_xx, compensation_xxx, compensation_xxxx,
                   num_consecutive_same_value, prev_value);
        }
      }

      // Calculate kurtosis for the current window
      output(i, col) =
          calc_kurt(minp, nobs, x, xx, xxx, xxxx, num_consecutive_same_value);

      // Reset state if bounds are not monotonic
      if (!is_monotonic_increasing_bounds) {
        nobs = 0;
        x = xx = xxx = xxxx = 0.0;
        compensation_remove_x = compensation_remove_xx =
            compensation_remove_xxx = compensation_remove_xxxx = 0.0;
      }
    }
  }
  return output;
}

DataFrame roll_quantile(const DataFrame &data, int window, double quantile,
                        int min_periods) {
  if (window <= 0) {
    throw std::invalid_argument("roll_quantile: window size must be positive.");
  }
  if (quantile < 0.0 || quantile > 1.0) {
    throw std::invalid_argument(
        "roll_quantile: quantile must be between 0.0 and 1.0.");
  }

  int N_rows = data.rows();
  int N_cols = data.cols();
  DataFrame output = DataFrame::Constant(N_rows, N_cols, NaN);

  // Create start and end arrays for rolling window bounds
  std::vector<int64_t> start(N_rows), end(N_rows);
  for (int i = 0; i < N_rows; ++i) {
    start[i] = std::max(0, i - window + 1);
    end[i] = i + 1;
  }

  // Process each column independently
  for (int col = 0; col < N_cols; ++col) {
    // Iterate through each window
    for (int i = 0; i < N_rows; ++i) {
      int64_t s = start[i];
      int64_t e = end[i];

      // For quantile, we always recalculate from scratch since it requires
      // sorting
      std::vector<double> window_values;
      for (int64_t j = s; j < e && j < N_rows; ++j) {
        double val = data(j, col);
        if (!std::isnan(val)) {
          window_values.push_back(val);
        }
      }

      // Calculate quantile for the current window
      if (window_values.size() >= static_cast<size_t>(min_periods)) {
        std::sort(window_values.begin(), window_values.end());

        size_t N = window_values.size();
        if (N == 1) {
          output(i, col) = window_values[0];
        } else {
          // Linear interpolation for quantile
          double pos = quantile * (static_cast<double>(N) - 1.0);
          int idx_floor = static_cast<int>(std::floor(pos));
          int idx_ceil = static_cast<int>(std::ceil(pos));
          double frac = pos - static_cast<double>(idx_floor);

          // Ensure indices are within bounds
          idx_floor = std::max(0, std::min(static_cast<int>(N - 1), idx_floor));
          idx_ceil = std::max(0, std::min(static_cast<int>(N - 1), idx_ceil));

          if (idx_floor == idx_ceil) { // pos is an integer
            output(i, col) = window_values[idx_floor];
          } else {
            output(i, col) = window_values[idx_floor] * (1.0 - frac) +
                             window_values[idx_ceil] * frac;
          }
        }
      } else {
        output(i, col) = NaN;
      }
    }
  }
  return output;
}

} // namespace rolling
} // namespace feature_operators
