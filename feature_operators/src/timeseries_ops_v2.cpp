#include "feature_operators/timeseries_ops_v2.hpp"
#include "feature_operators/rolling_aggregations.hpp"
#include <algorithm>
#include <iostream>
#include <stdexcept>
#include <vector>
#include <cmath>
#include <deque>
#include <map>
#include <numeric>

namespace feature_operators {
namespace v2 {

// ts series functions using rolling window aggregations
// These functions follow the same pattern as Python implementation

DataFrame ts_Mean_v2(const DataFrame &data, int n_param) {
  if (n_param < 0) {
    throw std::invalid_argument("ts_Mean_v2: window size n must be non-negative.");
  }
  if (n_param == 0) { // Python: if n == 0: return s1
    return data;
  }

  // Python: min_periods=1 for ts_Mean
  return rolling::roll_mean(data, n_param, 1);
}

DataFrame ts_Stdev_v2(const DataFrame &data, int n_param) {
  if (n_param < 0) {
    throw std::invalid_argument("ts_Stdev_v2: window size n must be non-negative.");
  }
  if (n_param == 0) { // Python: if n == 0: return s1
    return data;
  }

  // Python: min_periods=1, ddof=1 for ts_Stdev
  return rolling::roll_std(data, n_param, 1, 1);
}

DataFrame ts_Sum_v2(const DataFrame &data, int n_param) {
  if (n_param < 0) {
    throw std::invalid_argument("ts_Sum_v2: window size n must be non-negative.");
  }
  if (n_param == 0) { // Python: if n == 0: return s1
    return data;
  }

  // Python: min_periods=1 for ts_Sum
  return rolling::roll_sum(data, n_param, 1);
}

DataFrame ts_Min_v2(const DataFrame &data, int n_param) {
  if (n_param < 0) {
    throw std::invalid_argument("ts_Min_v2: window size n must be non-negative.");
  }
  if (n_param == 0) { // Python: if n == 0: return s1
    return data;
  }

  // Python: min_periods=1 for ts_Min
  return rolling::roll_min(data, n_param, 1);
}

DataFrame ts_Max_v2(const DataFrame &data, int n_param) {
  if (n_param < 0) {
    throw std::invalid_argument("ts_Max_v2: window size n must be non-negative.");
  }
  if (n_param == 0) { // Python: if n == 0: return s1
    return data;
  }

  // Python: min_periods=1 for ts_Max
  return rolling::roll_max(data, n_param, 1);
}

DataFrame ts_Median_v2(const DataFrame &data, int n_param) {
  if (n_param < 0) {
    throw std::invalid_argument("ts_Median_v2: window size n must be non-negative.");
  }
  if (n_param == 0) { // Python: if n == 0: return s1
    return data;
  }

  // Python: if NPrds < 3: NPrds = 3, min_periods=1
  int window_size = std::max(3, n_param);
  return rolling::roll_median(data, window_size, 1);
}

DataFrame ts_Skewness_v2(const DataFrame &data, int n_param) {
  if (n_param < 0) {
    throw std::invalid_argument("ts_Skewness_v2: window size n must be non-negative.");
  }
  if (n_param == 0) { // Python: if n == 0: return s1
    return data;
  }

  // Python: if n <= 2: n = 3
  int window_size = (n_param <= 2) ? 3 : n_param;

  // Python: min_periods=1 for ts_Skewness
  return rolling::roll_skew(data, window_size, 1);
}

DataFrame ts_Kurtosis_v2(const DataFrame &data, int n_param) {
  if (n_param < 0) {
    throw std::invalid_argument("ts_Kurtosis_v2: window size n must be non-negative.");
  }
  if (n_param == 0) { // Python: if n == 0: return s1
    return data;
  }

  // Python: if n <= 3: n = 4
  int window_size = (n_param <= 3) ? 4 : n_param;

  // Python: min_periods=1 for ts_Kurtosis
  return rolling::roll_kurt(data, window_size, 1);
}

DataFrame ts_Quantile_v2(const DataFrame &data, int n_param, char rettype) {
  if (n_param < 0) {
    throw std::invalid_argument("ts_Quantile_v2: window size n must be non-negative.");
  }
  if (n_param == 0) { // Python: if n == 0: return s1
    return data;
  }

  // Python: if NPrds < 5: NPrds = 5
  int window_size = std::max(5, n_param);

  double quantile;
  switch (rettype) {
  case 'A':
    quantile = 0.2;
    break;
  case 'B':
    quantile = 0.4;
    break;
  case 'C':
    quantile = 0.6;
    break;
  case 'D':
    quantile = 0.8;
    break;
  default:
    throw std::invalid_argument(
        "ts_Quantile_v2: rettype must be 'A', 'B', 'C', or 'D'.");
  }

  // Python: data.rolling(window=N).quantile() uses default min_periods=window_size
  // This means the first (window_size-1) positions will be NaN
  return rolling::roll_quantile(data, window_size, quantile, window_size);
}

DataFrame ts_Delay_v2(const DataFrame& data, int n) {
  if (n < 0) {
    throw std::invalid_argument("ts_Delay_v2: lag n must be non-negative.");
  }
  if (n == 0) {
    return data;
  }

  int N_rows = data.rows();
  int N_cols = data.cols();
  DataFrame output = DataFrame::Constant(N_rows, N_cols, rolling::NaN);

  // Copy data with lag
  for (int col = 0; col < N_cols; ++col) {
    for (int i = n; i < N_rows; ++i) {
      output(i, col) = data(i - n, col);
    }
  }
  return output;
}

DataFrame ts_Delta_v2(const DataFrame& data, int n) {
  if (n < 0) {
    throw std::invalid_argument("ts_Delta_v2: lag n must be non-negative.");
  }
  if (n == 0) {
    return DataFrame::Zero(data.rows(), data.cols());
  }

  DataFrame result = DataFrame::Constant(data.rows(), data.cols(), std::numeric_limits<double>::quiet_NaN());

  if (n >= data.rows()) {
    return result; // 如果延迟量 n 大于等于数据行数，则全部为 NaN
  }

  // 使用 Eigen 的切片操作进行高效计算 (完全参考原始实现)
  result.bottomRows(data.rows() - n) = data.bottomRows(data.rows() - n) - data.topRows(data.rows() - n);

  return result;
}

// DataFrame ts_Scale_v2(const DataFrame& data, int n) {
//   if (n <= 1) {
//     return DataFrame::Constant(data.rows(), data.cols(), std::numeric_limits<double>::quiet_NaN());
//   }

//   // 直接使用优化的滚动函数，避免重复计算
//   DataFrame roll_min_data = rolling::roll_min(data, n, 1);
//   DataFrame roll_max_data = rolling::roll_max(data, n, 1);

//   DataFrame result = DataFrame::Constant(data.rows(), data.cols(), std::numeric_limits<double>::quiet_NaN());

//   // 使用 Eigen 的向量化操作
//   for (int i = 0; i < data.rows(); ++i) {
//     for (int j = 0; j < data.cols(); ++j) {
//       double val = data(i, j);
//       double min_val = roll_min_data(i, j);
//       double max_val = roll_max_data(i, j);

//       if (!std::isnan(val) && !std::isnan(min_val) && !std::isnan(max_val)) {
//         double range = max_val - min_val;
//         if (std::abs(range) > 1e-14) {
//           result(i, j) = (val - min_val) / range;
//         } else {
//           result(i, j) = 0.0;
//         }
//       }
//     }
//   }
//   return result;
// }

DataFrame ts_Divide_v2(const DataFrame& data, int n) {
  if (n < 0) {
    throw std::invalid_argument("ts_Divide_v2: lag n must be non-negative.");
  }

  // Python: if NPrds < 5: NPrds = 5
  int n_effective = (n < 5) ? 5 : n;

  if (n == 0) {
    return DataFrame::Ones(data.rows(), data.cols());
  }

  int N_rows = data.rows();
  int N_cols = data.cols();
  DataFrame output = DataFrame::Constant(N_rows, N_cols, rolling::NaN);

  // Python: data / data.shift(NPrds)
  for (int col = 0; col < N_cols; ++col) {
    for (int i = n_effective; i < N_rows; ++i) {
      double current = data(i, col);
      double lagged = data(i - n_effective, col);

      if (!std::isnan(current) && !std::isnan(lagged)) {
        if (std::abs(lagged) > 1e-15) { // Avoid division by zero
          output(i, col) = current / lagged;
        } else {
          output(i, col) = rolling::NaN; // Python behavior for division by zero
        }
      }
    }
  }
  return output;
}

DataFrame ts_ChgRate_v2(const DataFrame& data, int n) {
  if (n < 0) {
    throw std::invalid_argument("ts_ChgRate_v2: lag n must be non-negative.");
  }

  // Python: if NPrds < 1: NPrds = 1
  int n_effective = (n < 1) ? 1 : n;

  if (n == 0) {
    return DataFrame::Zero(data.rows(), data.cols());
  }

  int N_rows = data.rows();
  int N_cols = data.cols();
  DataFrame output = DataFrame::Constant(N_rows, N_cols, rolling::NaN);

  // Python: data / data.shift(NPrds) - 1
  for (int col = 0; col < N_cols; ++col) {
    for (int i = n_effective; i < N_rows; ++i) {
      double current = data(i, col);
      double lagged = data(i - n_effective, col);

      if (!std::isnan(current) && !std::isnan(lagged)) {
        if (std::abs(lagged) > 1e-15) { // Avoid division by zero
          output(i, col) = current / lagged - 1.0;
        } else {
          output(i, col) = rolling::NaN; // Python behavior for division by zero
        }
      }
    }
  }
  return output;
}

DataFrame ts_Product_v2(const DataFrame& data, int n) {
  if (n < 0) {
    throw std::invalid_argument("ts_Product_v2: window size n must be non-negative.");
  }

  // Python: if NPrds < 3: NPrds = 3
  int n_effective = (n < 3) ? 3 : n;

  if (n == 0) {
    return DataFrame::Ones(data.rows(), data.cols());
  }

  int N_rows = data.rows();
  int N_cols = data.cols();
  DataFrame output = DataFrame::Constant(N_rows, N_cols, rolling::NaN);

  // Python implementation:
  // tmpData = data.copy()
  // tmpData[np.isnan(tmpData)] = 1
  // newData = _ts_Fun(tmpData, 'lambda x: np.prod(x, 0)', NPrds)
  // _ts_Fun processes from range(NPrds - 1, data.shape[0])

  // Step 1: Create tmpData with NaN replaced by 1
  DataFrame tmpData = data;
  for (int i = 0; i < N_rows; ++i) {
    for (int j = 0; j < N_cols; ++j) {
      if (std::isnan(tmpData(i, j))) {
        tmpData(i, j) = 1.0;
      }
    }
  }

  // Step 2: Apply _ts_Fun logic - start from row (n_effective-1)
  // Python: range(NPrds - 1, data.shape[0]) means start from row (NPrds-1)
  for (int col = 0; col < N_cols; ++col) {
    for (int i = n_effective - 1; i < N_rows; ++i) {
      // Python: iData = data_Trd[i - NPrds + 1:i + 1, :]
      double product = 1.0;

      for (int j = 0; j < n_effective; ++j) {
        int data_idx = i - n_effective + 1 + j;
        if (data_idx >= 0 && data_idx < N_rows) {
          double val = tmpData(data_idx, col);
          product *= val;
        }
      }

      output(i, col) = product;
    }
  }
  return output;
}

DataFrame ts_Argmax_v2(const DataFrame& data, int n) {
  int n_effective = std::max(3, n);

  DataFrame result = DataFrame::Constant(data.rows(), data.cols(), rolling::NaN);

  // Exactly replicate Python implementation with Eigen vectorization:
  // 1. tmpData = data.copy()
  // 2. tmpData[np.isnan(tmpData)] = -np.inf
  // 3. newData = NPrds - _ts_Fun(tmpData, 'lambda x: np.argmax(x, axis = 0)', NPrds)
  // 4. _ts_Fun processes from range(NPrds - 1, data.shape[0])

  // Step 1: Create tmpData with NaN replaced by -inf using Eigen vectorization
  DataFrame tmpData = data;
  const double neg_inf = -std::numeric_limits<double>::infinity();

  // Vectorized NaN replacement using Eigen's unaryExpr
  tmpData = tmpData.unaryExpr([neg_inf](double x) {
    return std::isnan(x) ? neg_inf : x;
  });

  // Step 2: Apply _ts_Fun logic with optimized argmax
  // Python: range(NPrds - 1, data.shape[0]) means start from row (NPrds-1)

  // Process each column independently for better cache locality
  for (Eigen::Index j = 0; j < data.cols(); ++j) {
    // Extract column once for better memory access pattern
    const auto& col_data = tmpData.col(j);

    for (Eigen::Index i = n_effective - 1; i < data.rows(); ++i) {
      // Python: iData = data_Trd[i - NPrds + 1:i + 1, :]
      Eigen::Index start_row = i - n_effective + 1;

      // Use Eigen's segment for efficient window extraction
      // segment() returns a view, not a copy, so it's very efficient
      auto window_segment = col_data.segment(start_row, n_effective);

      // Step 3: Apply np.argmax using Eigen's maxCoeff with index
      // This is highly optimized and uses SIMD when possible
      Eigen::Index argmax_idx;
      window_segment.maxCoeff(&argmax_idx);

      // Step 4: Python formula: NPrds - argmax_result
      result(i, j) = static_cast<double>(n_effective - argmax_idx);
    }
  }
  return result;
}

DataFrame ts_Argmin_v2(const DataFrame& data, int n) {
  int n_effective = std::max(3, n);

  DataFrame result = DataFrame::Constant(data.rows(), data.cols(), rolling::NaN);

  // Exactly replicate Python implementation with Eigen vectorization:
  // 1. tmpData = data.copy()
  // 2. tmpData[np.isnan(tmpData)] = np.inf
  // 3. newData = NPrds - _ts_Fun(tmpData, 'lambda x: np.argmin(x, axis = 0)', NPrds)
  // 4. _ts_Fun processes from range(NPrds - 1, data.shape[0])

  // Step 1: Create tmpData with NaN replaced by +inf using Eigen vectorization
  DataFrame tmpData = data;
  const double pos_inf = std::numeric_limits<double>::infinity();

  // Vectorized NaN replacement using Eigen's unaryExpr
  tmpData = tmpData.unaryExpr([pos_inf](double x) {
    return std::isnan(x) ? pos_inf : x;
  });

  // Step 2: Apply _ts_Fun logic with optimized argmin
  // Python: range(NPrds - 1, data.shape[0]) means start from row (NPrds-1)

  // Process each column independently for better cache locality
  for (Eigen::Index j = 0; j < data.cols(); ++j) {
    // Extract column once for better memory access pattern
    const auto& col_data = tmpData.col(j);

    for (Eigen::Index i = n_effective - 1; i < data.rows(); ++i) {
      // Python: iData = data_Trd[i - NPrds + 1:i + 1, :]
      Eigen::Index start_row = i - n_effective + 1;

      // Use Eigen's segment for efficient window extraction
      // segment() returns a view, not a copy, so it's very efficient
      auto window_segment = col_data.segment(start_row, n_effective);

      // Step 3: Apply np.argmin using Eigen's minCoeff with index
      // This is highly optimized and uses SIMD when possible
      Eigen::Index argmin_idx;
      window_segment.minCoeff(&argmin_idx);

      // Step 4: Python formula: NPrds - argmin_result
      result(i, j) = static_cast<double>(n_effective - argmin_idx);
    }
  }
  return result;
}

DataFrame ts_Rank_v2(const DataFrame& data, int n) {
  if (n < 0) {
    throw std::invalid_argument("ts_Rank_v2: window size n must be non-negative.");
  }

  // Python: NPrds = max(5, n)
  int n_effective = std::max(5, n);

  if (n == 0) {
    return DataFrame::Constant(data.rows(), data.cols(), 0.5);
  }

  int N_rows = data.rows();
  int N_cols = data.cols();
  DataFrame output = DataFrame::Constant(N_rows, N_cols, rolling::NaN);

  // Create start and end arrays for rolling window bounds
  std::vector<int64_t> start(N_rows), end(N_rows);
  for (int i = 0; i < N_rows; ++i) {
    start[i] = std::max(0, i - n_effective + 1);
    end[i] = i + 1;
  }

  // Process each column independently
  for (int col = 0; col < N_cols; ++col) {
    // Iterate through each window
    for (int i = 0; i < N_rows; ++i) {
      int64_t s = start[i];
      int64_t e = end[i];

      double current_val = data(i, col);
      if (std::isnan(current_val)) {
        continue;
      }

      std::vector<double> window_values;
      for (int64_t j = s; j < e && j < N_rows; ++j) {
        double val = data(j, col);
        if (!std::isnan(val)) {
          window_values.push_back(val);
        }
      }

      if (window_values.size() >= 1) {  // min_periods=1
        // Calculate rank using pandas method: average rank for ties
        int count_less = 0;
        int count_equal = 0;
        for (double val : window_values) {
          if (val < current_val) {
            count_less++;
          } else if (val == current_val) {
            count_equal++;
          }
        }

        // Average rank for ties: (rank of first item in tied group) + (num_equal - 1) / 2.0
        // Rank of first item in tied group is count_less + 1
        double rank_avg = (count_less + 1.0) + (count_equal - 1.0) / 2.0;
        double pct_rank = rank_avg / static_cast<double>(window_values.size());

        // Python: rank(pct=True) - 1/NPrds/2
        output(i, col) = pct_rank - 1.0 / static_cast<double>(n_effective) / 2.0;
      }
    }
  }
  return output;
}

// Tot_ series wrapper functions (fixed window of 15)
DataFrame Tot_Mean_v2(const DataFrame& data) {
  return ts_Mean_v2(data, 15);
}

DataFrame Tot_Sum_v2(const DataFrame& data) {
  return ts_Sum_v2(data, 15);
}

DataFrame Tot_Stdev_v2(const DataFrame& data) {
  return ts_Stdev_v2(data, 15);
}

DataFrame Tot_Delta_v2(const DataFrame& data) {
  return ts_Delta_v2(data, 15);
}

DataFrame Tot_Divide_v2(const DataFrame& data) {
  return ts_Divide_v2(data, 15);
}

DataFrame Tot_ChgRate_v2(const DataFrame& data) {
  return ts_ChgRate_v2(data, 15);
}

DataFrame Tot_Rank_v2(const DataFrame& data) {
  return ts_Rank_v2(data, 15);
}

DataFrame Tot_Min_v2(const DataFrame& data) {
  return ts_Min_v2(data, 15);
}

DataFrame Tot_Max_v2(const DataFrame& data) {
  return ts_Max_v2(data, 15);
}

DataFrame Tot_ArgMax_v2(const DataFrame& data) {
  return ts_Argmax_v2(data, 15);
}

DataFrame Tot_ArgMin_v2(const DataFrame& data) {
  return ts_Argmin_v2(data, 15);
}

// Advanced functions that require more complex implementations

DataFrame ts_Corr_v2(const DataFrame &data1, const DataFrame &data2, int n_param) {
  if (data1.rows() != data2.rows() || data1.cols() != data2.cols()) {
    throw std::invalid_argument(
        "ts_Corr: data1 and data2 must have the same dimensions.");
  }

  int n_effective = (n_param <= 1) ? 2 : n_param;
  int min_periods = 2;

  DataFrame result = DataFrame::Constant(data1.rows(), data1.cols(), rolling::NaN);

  for (Eigen::Index j = 0; j < data1.cols(); ++j) {
    double current_sum_x = 0.0, c_sum_x = 0.0;
    double current_sum_y = 0.0, c_sum_y = 0.0;
    double current_sum_x_sq = 0.0, c_sum_x_sq = 0.0;
    double current_sum_y_sq = 0.0, c_sum_y_sq = 0.0;
    double current_sum_xy = 0.0, c_sum_xy = 0.0;
    int current_valid_pairs_count = 0;

    for (Eigen::Index i = 0; i < data1.rows(); ++i) {
      // Add incoming pair
      double val1_in = data1(i, j);
      double val2_in = data2(i, j);
      if (!std::isnan(val1_in) && !std::isnan(val2_in)) {
        current_valid_pairs_count++;
        double val1_in_sq = val1_in * val1_in;
        double val2_in_sq = val2_in * val2_in;
        double val_xy_in = val1_in * val2_in;

        // Kahan sum for sum_x
        double y = val1_in - c_sum_x;
        double t = current_sum_x + y;
        c_sum_x = (t - current_sum_x) - y;
        current_sum_x = t;
        // Kahan sum for sum_y
        y = val2_in - c_sum_y;
        t = current_sum_y + y;
        c_sum_y = (t - current_sum_y) - y;
        current_sum_y = t;
        // Kahan sum for sum_x_sq
        y = val1_in_sq - c_sum_x_sq;
        t = current_sum_x_sq + y;
        c_sum_x_sq = (t - current_sum_x_sq) - y;
        current_sum_x_sq = t;
        // Kahan sum for sum_y_sq
        y = val2_in_sq - c_sum_y_sq;
        t = current_sum_y_sq + y;
        c_sum_y_sq = (t - current_sum_y_sq) - y;
        current_sum_y_sq = t;
        // Kahan sum for sum_xy
        y = val_xy_in - c_sum_xy;
        t = current_sum_xy + y;
        c_sum_xy = (t - current_sum_xy) - y;
        current_sum_xy = t;
      }

      // Subtract outgoing pair
      if (i >= n_effective) {
        double val1_out = data1(i - n_effective, j);
        double val2_out = data2(i - n_effective, j);
        if (!std::isnan(val1_out) && !std::isnan(val2_out)) {
          current_valid_pairs_count--;
          double val1_out_sq = val1_out * val1_out;
          double val2_out_sq = val2_out * val2_out;
          double val_xy_out = val1_out * val2_out;

          // Kahan sum for sum_x (subtracting)
          double y = -val1_out - c_sum_x;
          double t = current_sum_x + y;
          c_sum_x = (t - current_sum_x) - y;
          current_sum_x = t;
          // Kahan sum for sum_y (subtracting)
          y = -val2_out - c_sum_y;
          t = current_sum_y + y;
          c_sum_y = (t - current_sum_y) - y;
          current_sum_y = t;
          // Kahan sum for sum_x_sq (subtracting)
          y = -val1_out_sq - c_sum_x_sq;
          t = current_sum_x_sq + y;
          c_sum_x_sq = (t - current_sum_x_sq) - y;
          current_sum_x_sq = t;
          // Kahan sum for sum_y_sq (subtracting)
          y = -val2_out_sq - c_sum_y_sq;
          t = current_sum_y_sq + y;
          c_sum_y_sq = (t - current_sum_y_sq) - y;
          current_sum_y_sq = t;
          // Kahan sum for sum_xy (subtracting)
          y = -val_xy_out - c_sum_xy;
          t = current_sum_xy + y;
          c_sum_xy = (t - current_sum_xy) - y;
          current_sum_xy = t;
        }
      }

      if (current_valid_pairs_count >= min_periods) {
        double N_double = static_cast<double>(current_valid_pairs_count);
        double mean_x = current_sum_x / N_double;
        double mean_y = current_sum_y / N_double;
        double mean_xy = current_sum_xy / N_double;
        double mean_x_sq = current_sum_x_sq / N_double;
        double mean_y_sq = current_sum_y_sq / N_double;

        double var_x_pop = mean_x_sq - mean_x * mean_x;
        double var_y_pop = mean_y_sq - mean_y * mean_y;

        if (var_x_pop < 0 && var_x_pop > -1e-12)
          var_x_pop = 0;
        if (var_y_pop < 0 && var_y_pop > -1e-12)
          var_y_pop = 0;

        if (var_x_pop <= 1e-14 || var_y_pop <= 1e-14) {
          result(i, j) = rolling::NaN;
        } else {
          double std_x_pop = std::sqrt(var_x_pop);
          double std_y_pop = std::sqrt(var_y_pop);
          double cov_pop =
              mean_xy - mean_x * mean_y; // This is also used by ts_Cov
          result(i, j) = cov_pop / (std_x_pop * std_y_pop);
        }
      } else {
        result(i, j) = rolling::NaN;;
      }
    }
  }
  return result;
}
DataFrame ts_Cov_v2(const DataFrame &data1, const DataFrame &data2, int n_param) {
  if (data1.rows() != data2.rows() || data1.cols() != data2.cols()) {
    throw std::invalid_argument(
        "ts_Cov: data1 and data2 must have the same dimensions.");
  }

  // Python: if n <= 2: n = 2
  int n_effective = (n_param <= 2) ? 2 : n_param;
  int min_periods = 1;  // Python uses min_periods=1

  DataFrame result = DataFrame::Constant(data1.rows(), data1.cols(), rolling::NaN);

  for (Eigen::Index j = 0; j < data1.cols(); ++j) {
    double current_sum_x = 0.0, c_sum_x = 0.0;
    double current_sum_y = 0.0, c_sum_y = 0.0;
    double current_sum_xy = 0.0, c_sum_xy = 0.0;
    int current_valid_pairs_count = 0;

    for (Eigen::Index i = 0; i < data1.rows(); ++i) {
      // Add incoming pair with NaN propagation
      double val1_in = data1(i, j);
      double val2_in = data2(i, j);

      // Python NaN propagation: tem1[tem2.isna()] = np.nan; tem2[tem1.isna()] = np.nan
      if (std::isnan(val1_in) || std::isnan(val2_in)) {
        val1_in = rolling::NaN;
        val2_in = rolling::NaN;
      }

      if (!std::isnan(val1_in) && !std::isnan(val2_in)) {
        current_valid_pairs_count++;
        double val_xy_in = val1_in * val2_in;

        // Kahan sum for sum_x
        double y = val1_in - c_sum_x;
        double t = current_sum_x + y;
        c_sum_x = (t - current_sum_x) - y;
        current_sum_x = t;
        // Kahan sum for sum_y
        y = val2_in - c_sum_y;
        t = current_sum_y + y;
        c_sum_y = (t - current_sum_y) - y;
        current_sum_y = t;
        // Kahan sum for sum_xy
        y = val_xy_in - c_sum_xy;
        t = current_sum_xy + y;
        c_sum_xy = (t - current_sum_xy) - y;
        current_sum_xy = t;
      }

      // Subtract outgoing pair with NaN propagation - FIXED: only when window is full
      if (i >= n_effective) {
        double val1_out = data1(i - n_effective, j);
        double val2_out = data2(i - n_effective, j);

        // Apply same NaN propagation for outgoing pair
        if (std::isnan(val1_out) || std::isnan(val2_out)) {
          val1_out = rolling::NaN;
          val2_out = rolling::NaN;
        }

        if (!std::isnan(val1_out) && !std::isnan(val2_out)) {
          current_valid_pairs_count--;
          double val_xy_out = val1_out * val2_out;

          // Kahan sum for sum_x (subtracting)
          double y = -val1_out - c_sum_x;
          double t = current_sum_x + y;
          c_sum_x = (t - current_sum_x) - y;
          current_sum_x = t;
          // Kahan sum for sum_y (subtracting)
          y = -val2_out - c_sum_y;
          t = current_sum_y + y;
          c_sum_y = (t - current_sum_y) - y;
          current_sum_y = t;
          // Kahan sum for sum_xy (subtracting)
          y = -val_xy_out - c_sum_xy;
          t = current_sum_xy + y;
          c_sum_xy = (t - current_sum_xy) - y;
          current_sum_xy = t;
        }
      }

      if (current_valid_pairs_count >= min_periods) {
        double N_double = static_cast<double>(current_valid_pairs_count);
        double mean_x = current_sum_x / N_double;
        double mean_y = current_sum_y / N_double;
        double mean_xy = current_sum_xy / N_double;
        result(i, j) = mean_xy - mean_x * mean_y; // Population covariance
      } else {
        result(i, j) = rolling::NaN;
      }
    }
  }
  return result;
}

DataFrame ts_Scale_v2(const DataFrame &data, int n_param) {
  if (n_param <= 1) {
    return DataFrame::Constant(data.rows(), data.cols(), rolling::NaN);
  }
  int n_effective = n_param;
  // min_periods for min/max is 1 by default in their implementations.
  // For scaling, we need min and max to be valid and preferably different.

  DataFrame result = DataFrame::Constant(data.rows(), data.cols(), rolling::NaN);

  for (Eigen::Index j = 0; j < data.cols(); ++j) { // Iterate over columns
    for (Eigen::Index i = 0; i < data.rows();
         ++i) { // Iterate over rows (time points)
      double current_value = data(i, j);
      if (std::isnan(current_value)) {
        continue; // result(i, j) remains NaN
      }

      Eigen::Index start_row =
          std::max(static_cast<Eigen::Index>(0), i - n_effective + 1);
      Eigen::ArrayXd window_segment =
          data.col(j).segment(start_row, i - start_row + 1);

      double window_min = rolling::NaN;
      double window_max = rolling::NaN;
      int count_non_nan = 0;

      for (Eigen::Index k = 0; k < window_segment.size(); ++k) {
        if (!std::isnan(window_segment(k))) {
          if (count_non_nan == 0) {
            window_min = window_segment(k);
            window_max = window_segment(k);
          } else {
            if (window_segment(k) < window_min)
              window_min = window_segment(k);
            if (window_segment(k) > window_max)
              window_max = window_segment(k);
          }
          count_non_nan++;
        }
      }

      if (count_non_nan == 0 || std::isnan(window_min) ||
          std::isnan(window_max)) {
        // Not enough data, or min/max are NaN (should imply count_non_nan == 0)
        // result(i,j) remains NaN
        continue;
      }

      double range = window_max - window_min;

      if (std::abs(range) < 1e-14) { // Range is effectively zero
        // If range is zero, Python gives NaN for (val - min) / 0.
        // If val == min == max, then (x-x)/(x-x) = 0/0 -> NaN
        // If val != min but min==max, then (y-x)/0. This case should not happen
        // if min/max are calculated correctly. The only case for range ~ 0 is
        // if all non-NaN values in window are (almost) identical. In this case,
        // current_value should be equal to window_min. So (current_value -
        // window_min) is ~0. Result 0.0 / 0.0 -> NaN. Pandas behavior for x/0
        // where x is 0 is NaN. If all elements in window are same,
        // (val-val)/(val-val) -> NaN If only one element in window,
        // (val-val)/(val-val) -> NaN (already handled by n_param <=1 check)
        result(i, j) = rolling::NaN;
      } else {
        result(i, j) = (current_value - window_min) / range;
      }
    }
  }
  return result;
}

DataFrame ts_Decay_v2(const DataFrame& data, int n) {
  if (n < 0) {
    throw std::invalid_argument("ts_Decay_v2: window size n must be non-negative.");
  }
  if (n == 0) {
    return data;
  }

  int N_rows = data.rows();
  int N_cols = data.cols();
  DataFrame output = DataFrame::Constant(N_rows, N_cols, rolling::NaN);

  // Python implementation:
  // w = np.array([1-1/nPrds*(i-1) for i in range(nPrds,0,-1)])
  // w = w / sum(w)
  std::vector<double> weights(n);
  double weight_sum = 0.0;
  for (int i = 0; i < n; ++i) {
    weights[i] = 1.0 - (1.0 / n) * (n - i - 1);
    weight_sum += weights[i];
  }
  // Normalize weights
  for (int i = 0; i < n; ++i) {
    weights[i] /= weight_sum;
  }

  // Process each column independently
  for (int col = 0; col < N_cols; ++col) {
    for (int i = 0; i < N_rows; ++i) {
      if (i < n) {
        // Python: transformedTD[:nPrds] = np.nan
        output(i, col) = rolling::NaN;
      } else {
        // Python: transformedTD = dataTD*w[-1] + sum(np.roll(dataTD,shift=nPrds-i-1,axis=0)*w[i])
        double weighted_sum = 0.0;
        bool has_valid = false;

        for (int j = 0; j < n; ++j) {
          int shift = n - j - 1;
          int idx = i - shift;
          if (idx >= 0 && idx < N_rows) {
            double val = data(idx, col);
            if (!std::isnan(val)) {
              weighted_sum += val * weights[j];
              has_valid = true;
            }
          }
        }

        if (has_valid) {
          output(i, col) = weighted_sum;
        }
      }
    }
  }
  return output;
}

DataFrame ts_Decay2_v2(const DataFrame& data, int n) {
  if (n < 0) {
    throw std::invalid_argument("ts_Decay2_v2: window size n must be non-negative.");
  }
  if (n == 0) {
    return data;
  }

  int N_rows = data.rows();
  int N_cols = data.cols();
  DataFrame output = DataFrame::Constant(N_rows, N_cols, rolling::NaN);

  // Python implementation:
  // alpha = 1 - 2/(nPrds+1)
  // w = np.array([alpha**i for i in range(nPrds,0,-1)])
  // w = w / sum(w)
  double alpha = 1.0 - 2.0 / (n + 1.0);
  std::vector<double> weights(n);
  double weight_sum = 0.0;
  for (int i = 0; i < n; ++i) {
    weights[i] = std::pow(alpha, n - i);
    weight_sum += weights[i];
  }
  // Normalize weights
  for (int i = 0; i < n; ++i) {
    weights[i] /= weight_sum;
  }

  // Process each column independently
  for (int col = 0; col < N_cols; ++col) {
    for (int i = 0; i < N_rows; ++i) {
      if (i < n) {
        // Python: transformedTD[:nPrds] = np.nan
        output(i, col) = rolling::NaN;
      } else {
        // Python: transformedTD = dataTD*w[-1] + sum(np.roll(dataTD,shift=nPrds-i-1,axis=0)*w[i])
        double weighted_sum = 0.0;
        bool has_valid = false;

        for (int j = 0; j < n; ++j) {
          int shift = n - j - 1;
          int idx = i - shift;
          if (idx >= 0 && idx < N_rows) {
            double val = data(idx, col);
            if (!std::isnan(val)) {
              weighted_sum += val * weights[j];
              has_valid = true;
            }
          }
        }

        if (has_valid) {
          output(i, col) = weighted_sum;
        }
      }
    }
  }
  return output;
}

DataFrame ts_MaxDD_v2(const DataFrame& data, int n) {
  if (n < 0) {
    throw std::invalid_argument("ts_MaxDD_v2: window size n must be non-negative.");
  }
  if (n == 0) {
    return DataFrame::Zero(data.rows(), data.cols());
  }

  // Python: if NPrds < 3: NPrds = 3
  // Python: if NPrds < 5: NPrds = 5
  int n_effective = std::max(5, std::max(3, n));

  int N_rows = data.rows();
  int N_cols = data.cols();
  DataFrame output = DataFrame::Constant(N_rows, N_cols, rolling::NaN);

  // Python: for i in range(NPrds - 1, data_Trd.shape[0])
  // This means start from row (NPrds-1), first (NPrds-1) rows are NaN
  for (int col = 0; col < N_cols; ++col) {
    for (int i = n_effective - 1; i < N_rows; ++i) {
      // Python: tData = data_Trd[i - NPrds + 1:i + 1, :] / AdjMT[i, :]
      // Since AdjMT[i, :] is all 1s when Type=0, we can ignore the division

      // Python algorithm from __maxDD_fun:
      // pMax = np.full((1, tData.shape[1]), 1e-10)
      // pMaxDD = np.full((1, tData.shape[1]), 0)
      double pMax = 1e-10;
      double pMaxDD = 0.0;

      // Python: for j in range(NPrds):
      for (int j = 0; j < n_effective; ++j) {
        int data_idx = i - n_effective + 1 + j;
        if (data_idx >= 0 && data_idx < N_rows) {
          double val = data(data_idx, col);

          if (!std::isnan(val)) {
            // Python: pMax = np.maximum(pMax, tData[j, :])
            pMax = std::max(pMax, val);

            // Python: pMaxDD = np.maximum(pMaxDD, 1 - tData[j, :] / pMax)
            double drawdown = 1.0 - val / pMax;
            pMaxDD = std::max(pMaxDD, drawdown);

            // Python: pMaxDD[np.isnan(pMaxDD)] = 0
            if (std::isnan(pMaxDD)) {
              pMaxDD = 0.0;
            }
          }
        }
      }

      output(i, col) = pMaxDD;
    }
  }
  return output;
}

DataFrame ts_MeanChg_v2(const DataFrame& data, int n) {
  if (n < 0) {
    throw std::invalid_argument("ts_MeanChg_v2: window size n must be non-negative.");
  }
  if (n == 0) {
    return DataFrame::Zero(data.rows(), data.cols());
  }

  // Calculate rolling mean and rolling linear decay mean
  DataFrame roll_mean_data = rolling::roll_mean(data, n, 1);
  DataFrame decay_mean_data = ts_Decay_v2(data, n);

  int N_rows = data.rows();
  int N_cols = data.cols();
  DataFrame output = DataFrame::Constant(N_rows, N_cols, rolling::NaN);

  // Calculate difference
  for (int i = 0; i < N_rows; ++i) {
    for (int j = 0; j < N_cols; ++j) {
      double mean_val = roll_mean_data(i, j);
      double decay_val = decay_mean_data(i, j);

      if (!std::isnan(mean_val) && !std::isnan(decay_val)) {
        output(i, j) = mean_val - decay_val;
      }
    }
  }
  return output;
}

// Complex functions implementations
DataFrame ts_Regression_v2(const DataFrame& data1, const DataFrame& data2, int n, char rettype) {
  if (n < 0) {
    throw std::invalid_argument("ts_Regression_v2: window size n must be non-negative.");
  }
  if (data1.rows() != data2.rows() || data1.cols() != data2.cols()) {
    throw std::invalid_argument("ts_Regression_v2: data1 and data2 must have the same dimensions.");
  }

  // Python: if n <= 3: n = 3
  int n_effective = (n <= 3) ? 3 : n;

  // Python implementation:
  // tem1[tem2.isna()] = np.nan
  // tem2[tem1.isna()] = np.nan
  DataFrame tem1 = data1;
  DataFrame tem2 = data2;

  // Set NaN where either is NaN
  for (int i = 0; i < tem1.rows(); ++i) {
    for (int j = 0; j < tem1.cols(); ++j) {
      if (std::isnan(tem2(i, j))) {
        tem1(i, j) = rolling::NaN;
      }
      if (std::isnan(tem1(i, j))) {
        tem2(i, j) = rolling::NaN;
      }
    }
  }

  // Python: tem1_m = tem1.rolling(n, axis=0, min_periods=1).mean()
  DataFrame tem1_m = rolling::roll_mean(tem1, n_effective, 1);
  DataFrame tem2_m = rolling::roll_mean(tem2, n_effective, 1);

  // Python: tem_prod_m = (tem1 * tem2).rolling(n, axis=0, min_periods=1).mean()
  DataFrame tem_prod = tem1.cwiseProduct(tem2);
  DataFrame tem_prod_m = rolling::roll_mean(tem_prod, n_effective, 1);

  // Python: tem2_var = tem2.rolling(n, axis=0, min_periods=1).var(ddof=0)
  DataFrame tem2_var = rolling::roll_var(tem2, n_effective, 1);

  // Python: beta = (tem_prod_m - tem1_m * tem2_m) / tem2_var
  DataFrame numerator = tem_prod_m - tem1_m.cwiseProduct(tem2_m);
  DataFrame beta = DataFrame::Constant(data1.rows(), data1.cols(), rolling::NaN);

  for (int i = 0; i < beta.rows(); ++i) {
    for (int j = 0; j < beta.cols(); ++j) {
      double num = numerator(i, j);
      double var = tem2_var(i, j);

      if (!std::isnan(num) && !std::isnan(var) && std::abs(var) > 1e-15) {
        double beta_val = num / var;
        // Python: beta.replace([np.inf, -np.inf], np.nan)
        if (std::isinf(beta_val)) {
          beta(i, j) = rolling::NaN;
        } else {
          beta(i, j) = beta_val;
        }
      }
    }
  }

  // Python: if rettype == 'A': return beta
  if (rettype == 'A') {
    return beta;
  }

  // Python: const = tem1_m - beta * tem2_m
  DataFrame const_term = tem1_m - beta.cwiseProduct(tem2_m);

  // Python: if rettype == 'B': return const
  if (rettype == 'B') {
    return const_term;
  }

  // Python: y_est = const + beta * tem2
  DataFrame y_est = const_term + beta.cwiseProduct(tem2);

  // Python: if rettype == 'C': return y_est
  if (rettype == 'C') {
    return y_est;
  }

  // Python: resid = tem1 - y_est
  // Python: if rettype == 'D': return resid
  if (rettype == 'D') {
    return tem1 - y_est;
  }

  throw std::invalid_argument("ts_Regression_v2: rettype must be 'A', 'B', 'C', or 'D'.");
}

DataFrame ts_Entropy_v2(const DataFrame& data, int n, int bucket) {
  if (n < 0) {
    throw std::invalid_argument("ts_Entropy_v2: window size n must be non-negative.");
  }
  if (bucket <= 0) {
    throw std::invalid_argument("ts_Entropy_v2: bucket must be positive.");
  }

  int N_rows = data.rows();
  int N_cols = data.cols();
  DataFrame output = DataFrame::Constant(N_rows, N_cols, rolling::NaN);

  // Process each column independently
  for (int col = 0; col < N_cols; ++col) {
    // Python: for di in range(input.shape[0]):
    for (int i = 0; i < N_rows; ++i) {
      // Python: dcut = min(di + 1, days)
      int dcut = std::min(i + 1, n);

      // Python: cur = input[(di-dcut+1):(di+1)]
      std::vector<double> window_values;
      for (int j = i - dcut + 1; j <= i; ++j) {
        if (j >= 0 && j < N_rows) {
          double val = data(j, col);
          // Python: valid = np.isfinite(cur)
          if (std::isfinite(val)) {
            window_values.push_back(val);
          }
        }
      }

      // Python: if n > 2:
      if (window_values.size() > 2) {
        // Calculate histogram
        // Find min and max for binning
        double min_val = *std::min_element(window_values.begin(), window_values.end());
        double max_val = *std::max_element(window_values.begin(), window_values.end());

        // Create histogram bins
        std::vector<int> hist(bucket, 0);
        double bin_width = (max_val - min_val) / bucket;

        if (bin_width > 1e-15) { // Avoid division by zero
          for (double val : window_values) {
            int bin_idx = static_cast<int>((val - min_val) / bin_width);
            bin_idx = std::min(bin_idx, bucket - 1); // Clamp to last bin
            bin_idx = std::max(bin_idx, 0); // Clamp to first bin
            hist[bin_idx]++;
          }
        } else {
          // All values are the same, put everything in first bin
          hist[0] = window_values.size();
        }

        // Python: hist = np.histogram(hist, bucket)[0] / n
        // Python: hist[hist==0] = 1
        // Python: res[di] = np.log(bucket) - np.mean(hist * np.log(hist))
        double entropy = std::log(bucket);
        double mean_log_hist = 0.0;
        int valid_bins = 0;

        for (int bin_count : hist) {
          if (bin_count > 0) {
            double prob = static_cast<double>(bin_count) / window_values.size();
            mean_log_hist += prob * std::log(prob);
            valid_bins++;
          }
        }

        if (valid_bins > 0) {
          entropy += mean_log_hist; // Note: mean_log_hist is negative, so this is subtraction
          output(i, col) = entropy;
        }
      }
    }
  }
  return output;
}

DataFrame ts_Partial_corr_v2(const DataFrame& data1, const DataFrame& data2, const DataFrame& data3, int n) {
  if (n < 0) {
    throw std::invalid_argument("ts_Partial_corr_v2: window size n must be non-negative.");
  }
  if (data1.rows() != data2.rows() || data1.rows() != data3.rows() ||
      data1.cols() != data2.cols() || data1.cols() != data3.cols()) {
    throw std::invalid_argument("ts_Partial_corr_v2: all data must have the same dimensions.");
  }

  // Python implementation:
  // corr12 = ts_Corr(s1, s2, n)
  // corr13 = ts_Corr(s1, s3, n)
  // corr23 = ts_Corr(s2, s3, n)
  // res = (corr12 - corr13 * corr23) / (np.sqrt(1 - corr13**2) * np.sqrt(1 - corr23**2))
  // return res.replace([-np.inf, np.inf], np.nan)

  DataFrame corr12 = ts_Corr_v2(data1, data2, n);
  DataFrame corr13 = ts_Corr_v2(data1, data3, n);
  DataFrame corr23 = ts_Corr_v2(data2, data3, n);

  int N_rows = data1.rows();
  int N_cols = data1.cols();
  DataFrame result = DataFrame::Constant(N_rows, N_cols, rolling::NaN);

  for (int i = 0; i < N_rows; ++i) {
    for (int j = 0; j < N_cols; ++j) {
      double c12 = corr12(i, j);
      double c13 = corr13(i, j);
      double c23 = corr23(i, j);

      if (!std::isnan(c12) && !std::isnan(c13) && !std::isnan(c23)) {
        double denom1 = 1.0 - c13 * c13;
        double denom2 = 1.0 - c23 * c23;

        if (denom1 > 1e-15 && denom2 > 1e-15) {
          double numerator = c12 - c13 * c23;
          double denominator = std::sqrt(denom1) * std::sqrt(denom2);

          if (std::abs(denominator) > 1e-15) {
            double partial_corr = numerator / denominator;
            // Python: replace([-np.inf, np.inf], np.nan)
            if (std::isinf(partial_corr)) {
              result(i, j) = rolling::NaN;
            } else {
              result(i, j) = partial_corr;
            }
          }
        }
      }
    }
  }

  return result;
}

// Helper function for inverse normal CDF (approximation)
double inverse_normal_cdf(double p) {
  if (p <= 0.0 || p >= 1.0) {
    return rolling::NaN;
  }

  // Beasley-Springer-Moro algorithm (approximation)
  static const double a0 = -3.969683028665376e+01;
  static const double a1 = 2.209460984245205e+02;
  static const double a2 = -2.759285104469687e+02;
  static const double a3 = 1.383577518672690e+02;
  static const double a4 = -3.066479806614716e+01;
  static const double a5 = 2.506628277459239e+00;

  static const double b1 = -5.447609879822406e+01;
  static const double b2 = 1.615858368580409e+02;
  static const double b3 = -1.556989798598866e+02;
  static const double b4 = 6.680131188771972e+01;
  static const double b5 = -1.328068155288572e+01;

  double x = p - 0.5;
  double r;

  if (std::abs(x) < 0.42) {
    r = x * x;
    return x * (((((a5 * r + a4) * r + a3) * r + a2) * r + a1) * r + a0) /
           ((((((r + b5) * r + b4) * r + b3) * r + b2) * r + b1) * r + 1.0);
  } else {
    r = p;
    if (x > 0.0) r = 1.0 - p;
    r = std::log(-std::log(r));

    static const double c0 = -7.784894002430293e-03;
    static const double c1 = -3.223964580411365e-01;
    static const double c2 = -2.400758277161838e+00;
    static const double c3 = -2.549732539343734e+00;
    static const double c4 = 4.374664141464968e+00;
    static const double c5 = 2.938163982698783e+00;

    static const double d1 = 7.784695709041462e-03;
    static const double d2 = 3.224671290700398e-01;
    static const double d3 = 2.445134137142996e+00;
    static const double d4 = 3.754408661907416e+00;

    double result = c0 + r * (c1 + r * (c2 + r * (c3 + r * (c4 + r * c5)))) /
                    (1.0 + r * (d1 + r * (d2 + r * (d3 + r * d4))));

    if (x < 0.0) result = -result;
    return result;
  }
}

DataFrame ts_TransNorm_v2(const DataFrame& data, int n) {
  if (n < 0) {
    throw std::invalid_argument("ts_TransNorm_v2: window size n must be non-negative.");
  }

  // Python: if n < 3: n = 3
  int n_effective = (n < 3) ? 3 : n;

  // Python implementation:
  // dfCleaned = s1.copy()
  // df = (dfCleaned.rolling(window=n, min_periods=1).rank(ascending=True, pct=True)) - 1 / n / 2
  // z = np.array(df)
  // zz = norm.ppf(z)
  // zzz = pd.DataFrame(zz, index=df.index, columns=df.columns)
  // return zzz

  // Step 1: Calculate rolling rank (pct=True)
  DataFrame rank_data = DataFrame::Constant(data.rows(), data.cols(), rolling::NaN);

  // Create start and end arrays for rolling window bounds
  std::vector<int64_t> start(data.rows()), end(data.rows());
  for (int i = 0; i < data.rows(); ++i) {
    start[i] = std::max(0, i - n_effective + 1);
    end[i] = i + 1;
  }

  // Process each column independently
  for (int col = 0; col < data.cols(); ++col) {
    for (int i = 0; i < data.rows(); ++i) {
      int64_t s = start[i];
      int64_t e = end[i];

      double current_val = data(i, col);
      if (std::isnan(current_val)) {
        continue;
      }

      std::vector<double> window_values;
      for (int64_t j = s; j < e && j < data.rows(); ++j) {
        double val = data(j, col);
        if (!std::isnan(val)) {
          window_values.push_back(val);
        }
      }

      if (window_values.size() >= 1) {
        // Calculate percentile rank
        int count_less = 0;
        int count_equal = 0;
        for (double val : window_values) {
          if (val < current_val) {
            count_less++;
          } else if (val == current_val) {
            count_equal++;
          }
        }

        // Average rank for ties, then convert to percentile
        double rank = count_less + (count_equal + 1.0) / 2.0;
        double pct_rank = rank / window_values.size();

        rank_data(i, col) = pct_rank;
      }
    }
  }

  // Step 2: Apply transformation: rank - 1/(2*n)
  DataFrame adjusted_rank = DataFrame::Constant(data.rows(), data.cols(), rolling::NaN);
  double adjustment = 1.0 / (2.0 * n_effective);

  for (int i = 0; i < data.rows(); ++i) {
    for (int j = 0; j < data.cols(); ++j) {
      double rank_val = rank_data(i, j);
      if (!std::isnan(rank_val)) {
        adjusted_rank(i, j) = rank_val - adjustment;
      }
    }
  }

  // Step 3: Apply inverse normal CDF
  DataFrame result = DataFrame::Constant(data.rows(), data.cols(), rolling::NaN);

  for (int i = 0; i < data.rows(); ++i) {
    for (int j = 0; j < data.cols(); ++j) {
      double adj_rank = adjusted_rank(i, j);
      if (!std::isnan(adj_rank)) {
        // Clamp to valid range for inverse normal CDF
        adj_rank = std::max(1e-15, std::min(1.0 - 1e-15, adj_rank));
        result(i, j) = inverse_normal_cdf(adj_rank);
      }
    }
  }

  return result;
}

} // namespace v2
} // namespace feature_operators
