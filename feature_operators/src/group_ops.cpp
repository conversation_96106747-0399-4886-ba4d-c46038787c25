#include "feature_operators/group_ops.hpp"
#include "feature_operators/panel_ops.hpp" // For pn_rank_custom_norm, pn_trans_norm
#include "feature_operators/types.hpp" // Added
#include <Eigen/Dense> // Kept for other Eigen types
#include <vector>
#include <map>
#include <algorithm> // For std::sort, etc.
#include <cmath>     // For std::isnan
#include <limits>    // For std::numeric_limits
#include <cassert>   // For assert
#include <boost/math/distributions/normal.hpp> // For normal quantile

namespace feature_operators {

const double NaN = std::numeric_limits<double>::quiet_NaN();

// Helper function to calculate nanmean for a 1D Eigen Array/Vector
double calculate_nanmean(const Eigen::ArrayXd& arr) {
    double sum = 0;
    int count = 0;
    for (Eigen::Index i = 0; i < arr.size(); ++i) {
        if (!std::isnan(arr(i))) {
            sum += arr(i);
            count++;
        }
    }
    return (count > 0) ? (sum / count) : NaN;
}


DataFrame pn_GroupNeutral(const DataFrame& data, const DataFrame& labels) {
    assert(data.rows() == labels.rows() && data.cols() == labels.cols());
    DataFrame result = DataFrame::Constant(data.rows(), data.cols(), NaN);
    
    // Instantiate Boost normal distribution object once
    boost::math::normal dist(0.0, 1.0);

    for (Eigen::Index i = 0; i < data.rows(); ++i) { // For each row
        std::map<double, std::vector<Eigen::Index>> groups;
        for (Eigen::Index j = 0; j < data.cols(); ++j) {
            if (!std::isnan(labels(i, j)) && !std::isnan(data(i, j))) {
                groups[labels(i, j)].push_back(j);
            }
        }

        for (const auto& pair : groups) {
            const std::vector<Eigen::Index>& col_indices = pair.second;
            if (col_indices.empty()) continue;

            // Directly populate Eigen::ArrayXd, removing intermediate std::vector
            Eigen::ArrayXd group_values(col_indices.size());
            for (size_t k = 0; k < col_indices.size(); ++k) {
                // data(i, col_indices[k]) is the value for the current row i, and k-th column index in this group
                group_values(k) = data(i, col_indices[k]);
            }

            // The previous check `if (group_data_buffer.empty()) continue;` is covered by
            // `if (col_indices.empty()) continue;` because group_values will have size 0 if col_indices is empty.
            // calculate_nanmean handles empty or all-NaN arrays correctly by returning NaN.
            
            double group_mean = calculate_nanmean(group_values);

            if (!std::isnan(group_mean)) {
                Eigen::ArrayXd processed_group_data = group_values - group_mean;
                for (size_t k = 0; k < col_indices.size(); ++k) {
                    result(i, col_indices[k]) = processed_group_data(k);
                }
            } else { // group_mean is NaN (e.g. all NaNs in group_values)
                for (size_t k = 0; k < col_indices.size(); ++k) {
                    // If group_mean is NaN, it means all values in group_values were NaN (or group_values was empty).
                    // In this case, the original values (which are NaN) or just NaN should be assigned.
                    // The existing behavior is to assign NaN, which is fine.
                    result(i, col_indices[k]) = NaN; 
                }
            }
        }
    }
    return result;
}

DataFrame pn_GroupRank(const DataFrame& data, const DataFrame& labels) {
    assert(data.rows() == labels.rows() && data.cols() == labels.cols());
    DataFrame result = DataFrame::Constant(data.rows(), data.cols(), NaN);

    for (Eigen::Index i = 0; i < data.rows(); ++i) { // For each row
        std::map<double, std::vector<Eigen::Index>> groups;
        for (Eigen::Index j = 0; j < data.cols(); ++j) {
            if (!std::isnan(labels(i, j)) && !std::isnan(data(i, j))) {
                groups[labels(i, j)].push_back(j);
            }
        }

        for (const auto& pair : groups) {
            const std::vector<Eigen::Index>& col_indices = pair.second;
            if (col_indices.empty()) continue;

            // Directly populate Eigen::ArrayXd for the group's data for the current row
            Eigen::ArrayXd group_values(col_indices.size());
            for (size_t k = 0; k < col_indices.size(); ++k) {
                group_values(k) = data(i, col_indices[k]);
            }
            
            // The check for group_data_buffer.empty() is implicitly handled by
            // col_indices.empty() check above. If col_indices is not empty, group_values will have size > 0.
            // rank_row_custom_norm handles empty or all-NaN arrays by returning NaNs.

            // Call rank_row_custom_norm directly with the Eigen::ArrayXd
            // This function is now declared in panel_ops.hpp and defined in panel_ops.cpp
            Eigen::ArrayXd processed_group_data_array = rank_row_custom_norm(group_values); 
            
            // Assign results from the processed array to the result DataFrame
            for (size_t k = 0; k < col_indices.size(); ++k) {
                result(i, col_indices[k]) = processed_group_data_array(k);
            }
        }
    }
    return result;
}

// DataFrame pn_GroupNorm(const DataFrame& data, const DataFrame& labels) {
//     assert(data.rows() == labels.rows() && data.cols() == labels.cols());
//     DataFrame result = DataFrame::Constant(data.rows(), data.cols(), NaN);

//     for (Eigen::Index i = 0; i < data.rows(); ++i) { // For each row
//         std::map<double, std::vector<Eigen::Index>> groups;
//         for (Eigen::Index j = 0; j < data.cols(); ++j) {
//             if (!std::isnan(labels(i, j)) && !std::isnan(data(i, j))) {
//                 groups[labels(i, j)].push_back(j);
//             }
//         }

//         for (const auto& pair : groups) {
//             const std::vector<Eigen::Index>& col_indices = pair.second;
//             if (col_indices.empty()) continue;

//             // Directly populate Eigen::ArrayXd for the group's data for the current row
//             Eigen::ArrayXd group_values(col_indices.size());
//             for (size_t k = 0; k < col_indices.size(); ++k) {
//                 group_values(k) = data(i, col_indices[k]);
//             }
            
//             // Call rank_row_custom_norm directly
//             Eigen::ArrayXd ranked_group_values = rank_row_custom_norm(group_values);

//             // Apply pn_TransNorm logic using unaryExpr
//             Eigen::ArrayXd final_group_values = ranked_group_values.unaryExpr([&](double x) {
//                 if (std::isnan(x)) {
//                     return NaN;
//                 }
//                 // CLAMP_EPSILON is available from panel_ops.hpp
//                 double clamped_x = std::max(CLAMP_EPSILON, std::min(x, 1.0 - CLAMP_EPSILON));
                
//                 if (clamped_x <= 0.0 || clamped_x >= 1.0) { 
//                     return NaN; 
//                 }
//                 return boost::math::quantile(dist, clamped_x);
//             });
            
//             // Assign results from the processed array to the result DataFrame
//             for (size_t k = 0; k < col_indices.size(); ++k) {
//                 result(i, col_indices[k]) = final_group_values(k);
//             }
//         }
//     }
//     return result;
// }
DataFrame pn_GroupNorm(const DataFrame& data, const DataFrame& labels) {
    assert(data.rows() == labels.rows() && data.cols() == labels.cols());
    DataFrame result = DataFrame::Constant(data.rows(), data.cols(), NaN);

    // Declare and initialize the normal distribution object here
    // This creates a standard normal distribution (mean=0, stddev=1)
    boost::math::normal_distribution<> dist; 

    for (Eigen::Index i = 0; i < data.rows(); ++i) { // For each row
        std::map<double, std::vector<Eigen::Index>> groups;
        for (Eigen::Index j = 0; j < data.cols(); ++j) {
            if (!std::isnan(labels(i, j)) && !std::isnan(data(i, j))) {
                groups[labels(i, j)].push_back(j);
            }
        }

        for (const auto& pair : groups) {
            const std::vector<Eigen::Index>& col_indices = pair.second;
            if (col_indices.empty()) continue;

            // Directly populate Eigen::ArrayXd for the group's data for the current row
            Eigen::ArrayXd group_values(col_indices.size());
            for (size_t k = 0; k < col_indices.size(); ++k) {
                group_values(k) = data(i, col_indices[k]);
            }
            
            // Call rank_row_custom_norm directly
            Eigen::ArrayXd ranked_group_values = rank_row_custom_norm(group_values);

            // Apply pn_TransNorm logic using unaryExpr
            Eigen::ArrayXd final_group_values = ranked_group_values.unaryExpr([&](double x) {
                if (std::isnan(x)) {
                    return NaN;
                }
                // CLAMP_EPSILON is available from panel_ops.hpp
                double clamped_x = std::max(CLAMP_EPSILON, std::min(x, 1.0 - CLAMP_EPSILON));
                
                if (clamped_x <= 0.0 || clamped_x >= 1.0) { 
                    return NaN; 
                }
                // 'dist' is now in scope
                return boost::math::quantile(dist, clamped_x);
            });
            
            // Assign results from the processed array to the result DataFrame
            for (size_t k = 0; k < col_indices.size(); ++k) {
                result(i, col_indices[k]) = final_group_values(k);
            }
        }
    }
    return result;
}
} // namespace feature_operators
