// #include "feature_operators/logical_ops.hpp"
// #include "feature_operators/types.hpp" // Added
// #include <Eigen/Dense> // Kept for other Eigen types
// #include <limits> // For std::numeric_limits
// #include <cmath>  // For std::isnan
// #include <cassert> // for assert

// namespace feature_operators {

// const double NaN = std::numeric_limits<double>::quiet_NaN();

// // Helper to create an DataFrame from a scalar, matching dimensions of 'like'
// DataFrame scalar_to_array(double scalar_val, const DataFrame& like) {
//     return DataFrame::Constant(like.rows(), like.cols(), scalar_val);
// }

// // if_then_else_val: s1 > 0 ? then_val : (s1 < 0 ? else_val : NaN)
// DataFrame IfThen(const DataFrame& condition_data, const DataFrame& then_val, const DataFrame& else_val) {
//     assert(condition_data.rows() == then_val.rows() && condition_data.cols() == then_val.cols());
//     assert(condition_data.rows() == else_val.rows() && condition_data.cols() == else_val.cols());

//     // Define conditions using Eigen array operations
//     // .array() converts from Matrix expression to Array expression for coefficient-wise operations
//     auto cond_then = (condition_data.array() > 0).array() && (!condition_data.array().isNaN());
//     auto cond_else = (condition_data.array() < 0).array() && (!condition_data.array().isNaN());

//     // Create a DataFrame filled with NaN for the default case
//     DataFrame nan_fill_array = DataFrame::Constant(condition_data.rows(), condition_data.cols(), NaN);

//     // Nested select:
//     // 1. If cond_then is true, use then_val.
//     // 2. Else, evaluate the second select:
//     //    a. If cond_else is true, use else_val.
//     //    b. Else (neither cond_then nor cond_else is true), use nan_fill_array.
//     // .matrix() converts the result of array expression back to a Matrix (DataFrame)
//     DataFrame result = cond_then.select(then_val.array(), 
//                                        cond_else.select(else_val.array(), nan_fill_array.array())
//                                       ).matrix();
//     return result;
// }

// DataFrame IfThen(const DataFrame& condition_data, double then_scalar, const DataFrame& else_val) {
//     DataFrame then_arr = scalar_to_array(then_scalar, condition_data);
//     return IfThen(condition_data, then_arr, else_val);
// }

// DataFrame IfThen(const DataFrame& condition_data, const DataFrame& then_val, double else_scalar) {
//     DataFrame else_arr = scalar_to_array(else_scalar, condition_data);
//     return IfThen(condition_data, then_val, else_arr);
// }

// DataFrame IfThen(const DataFrame& condition_data, double then_scalar, double else_scalar) {
//     DataFrame then_arr = scalar_to_array(then_scalar, condition_data);
//     DataFrame else_arr = scalar_to_array(else_scalar, condition_data);
//     return IfThen(condition_data, then_arr, else_arr);
// }


// // and_val: Result is 0.0 if a or b is NaN. Otherwise, (a_val != 0 && b_val != 0) ? 1.0 : 0.0
// DataFrame And(const DataFrame& a, const DataFrame& b) {
//     assert(a.rows() == b.rows() && a.cols() == b.cols());
//     // Condition for non-NaN elements: (a != 0) AND (b != 0)
//     Eigen::Array<bool, Eigen::Dynamic, Eigen::Dynamic> condition = (a != 0.0) && (b != 0.0);
//     // Mask for elements where neither a nor b is NaN
//     Eigen::Array<bool, Eigen::Dynamic, Eigen::Dynamic> not_nan_mask = !a.isNaN() && !b.isNaN();
//     return not_nan_mask.select(condition.template cast<double>(), DataFrame::Zero(a.rows(), a.cols()));
// }

// DataFrame And(const DataFrame& a, double b_scalar) {
//     DataFrame b_arr = scalar_to_array(b_scalar, a);
//     return And(a, b_arr);
// }

// // or_val: Result is 0.0 if a or b is NaN. Otherwise, (a_val != 0 || b_val != 0) ? 1.0 : 0.0
// DataFrame Or(const DataFrame& a, const DataFrame& b) {
//     assert(a.rows() == b.rows() && a.cols() == b.cols());
//     // Condition for non-NaN elements: (a != 0) OR (b != 0)
//     Eigen::Array<bool, Eigen::Dynamic, Eigen::Dynamic> condition = (a != 0.0) || (b != 0.0);
//     // Mask for elements where neither a nor b is NaN
//     Eigen::Array<bool, Eigen::Dynamic, Eigen::Dynamic> not_nan_mask = !a.isNaN() && !b.isNaN();

//     return not_nan_mask.select(condition.template cast<double>(), DataFrame::Zero(a.rows(), a.cols()));
// }

// DataFrame Or(const DataFrame& a, double b_scalar) {
//     DataFrame b_arr = scalar_to_array(b_scalar, a);
//     return Or(a, b_arr);
// }

// // not_val: Result is 0.0 if a is NaN. Otherwise, (a_val == 0) ? 1.0 : 0.0
// DataFrame Not(const DataFrame& a) {
//     // Condition for non-NaN elements: (a == 0)
//     Eigen::Array<bool, Eigen::Dynamic, Eigen::Dynamic> condition = (a == 0.0);
//     // Mask for elements where a is not NaN
//     Eigen::Array<bool, Eigen::Dynamic, Eigen::Dynamic> not_nan_mask = !a.isNaN();

//     return not_nan_mask.select(condition.template cast<double>(), DataFrame::Zero(a.rows(), a.cols()));
// }

// // xor_val: Result is 0.0 if a or b is NaN. Otherwise, ((a_val != 0 && b_val == 0) || (a_val == 0 && b_val != 0)) ? 1.0 : 0.0
// DataFrame Xor(const DataFrame& a, const DataFrame& b) {
//     assert(a.rows() == b.rows() && a.cols() == b.cols());
//     // Condition for non-NaN elements: (a != 0 XOR b != 0)
//     Eigen::Array<bool, Eigen::Dynamic, Eigen::Dynamic> condition = ((a != 0.0) && (b == 0.0)) || ((a == 0.0) && (b != 0.0));
//     // Mask for elements where neither a nor b is NaN
//     Eigen::Array<bool, Eigen::Dynamic, Eigen::Dynamic> not_nan_mask = !a.isNaN() && !b.isNaN();

//     return not_nan_mask.select(condition.template cast<double>(), DataFrame::Zero(a.rows(), a.cols()));
// }

// DataFrame Xor(const DataFrame& a, double b_scalar) {
//     DataFrame b_arr = scalar_to_array(b_scalar, a);
//     return Xor(a, b_arr);
// }

// } // namespace feature_operators
