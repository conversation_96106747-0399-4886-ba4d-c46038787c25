#ifndef FEATURE_OPERATORS_XT_PANEL_OPS_HPP
#define FEATURE_OPERATORS_XT_PANEL_OPS_HPP

#include "feature_operators_xt/types.hpp"
#include <xtensor/xarray.hpp>
#include <xtensor/xmath.hpp>
#include <xtensor/xview.hpp>
#include <xtensor/xadapt.hpp>
#include <xtensor/xreducer.hpp>
#include <limits>
#include <cmath>

namespace feature_operators_xt {

// 辅助函数：计算忽略NaN的均值
template<typename T>
double compute_nanmean(const T& data) {
    double sum = 0.0;
    std::size_t count = 0;

    for (auto it = data.begin(); it != data.end(); ++it) {
        if (!std::isnan(*it)) {
            sum += *it;
            count++;
        }
    }

    return count > 0 ? sum / count : std::numeric_limits<double>::quiet_NaN();
}

// 辅助函数：计算忽略NaN的最大值
template<typename T>
double compute_nanmax(const T& data) {
    double max_val = -std::numeric_limits<double>::infinity();
    bool found_valid = false;

    for (auto it = data.begin(); it != data.end(); ++it) {
        if (!std::isnan(*it)) {
            if (!found_valid || *it > max_val) {
                max_val = *it;
                found_valid = true;
            }
        }
    }

    return found_valid ? max_val : std::numeric_limits<double>::quiet_NaN();
}

// 辅助函数：计算忽略NaN的最小值
template<typename T>
double compute_nanmin(const T& data) {
    double min_val = std::numeric_limits<double>::infinity();
    bool found_valid = false;

    for (auto it = data.begin(); it != data.end(); ++it) {
        if (!std::isnan(*it)) {
            if (!found_valid || *it < min_val) {
                min_val = *it;
                found_valid = true;
            }
        }
    }

    return found_valid ? min_val : std::numeric_limits<double>::quiet_NaN();
}

// 面板均值：计算每行的均值并复制到整行
template<typename T>
auto pn_Mean(const T& data) {
    auto result = xt::empty_like(data);

    for (std::size_t i = 0; i < data.shape(0); ++i) {
        auto row = xt::view(data, i, xt::all());
        auto row_mean = compute_nanmean(row);
        xt::view(result, i, xt::all()) = row_mean;
    }

    return result;
}

// 面板最大值填充
template<typename T>
auto pn_FillMax(const T& data) {
    auto result = xt::empty_like(data);

    for (std::size_t i = 0; i < data.shape(0); ++i) {
        auto row = xt::view(data, i, xt::all());
        auto row_max = compute_nanmax(row);
        xt::view(result, i, xt::all()) = row_max;
    }

    return result;
}

// 面板最小值填充
template<typename T>
auto pn_FillMin(const T& data) {
    auto result = xt::empty_like(data);

    for (std::size_t i = 0; i < data.shape(0); ++i) {
        auto row = xt::view(data, i, xt::all());
        auto row_min = compute_nanmin(row);
        xt::view(result, i, xt::all()) = row_min;
    }

    return result;
}

} // namespace feature_operators_xt

#endif // FEATURE_OPERATORS_XT_PANEL_OPS_HPP
    auto result = xt::empty_like(data);

    for (std::size_t i = 0; i < data.shape(0); ++i) {
        auto row = xt::view(data, i, xt::all());
        auto row_result = xt::view(result, i, xt::all());

        // 计算百分位排名
        std::vector<double> valid_values;
        std::vector<std::size_t> valid_indices;

        for (std::size_t j = 0; j < row.size(); ++j) {
            if (!std::isnan(row(j))) {
                valid_values.push_back(row(j));
                valid_indices.push_back(j);
            }
        }

        if (valid_values.empty()) {
            row_result.fill(std::numeric_limits<double>::quiet_NaN());
            continue;
        }

        // 计算排名
        for (std::size_t j = 0; j < valid_indices.size(); ++j) {
            std::size_t idx = valid_indices[j];
            double value = row(idx);

            std::size_t rank = 0;
            for (double v : valid_values) {
                if (v < value) rank++;
            }

            double pct_rank = static_cast<double>(rank) / valid_values.size();
            double min_rank = 1.0 / (2.0 * valid_values.size());
            row_result(idx) = pct_rank - min_rank;
        }

        // 设置NaN值
        for (std::size_t j = 0; j < row.size(); ++j) {
            if (std::isnan(row(j))) {
                row_result(j) = std::numeric_limits<double>::quiet_NaN();
            }
        }
    }

    return result;
}

// 面板截断：保留指定百分位范围内的值
template<typename T>
auto pn_Cut(const T& data, double lower_bound_pct = 20.0, double upper_bound_pct = 80.0) {
    auto rank_data = pn_Rank(data);
    auto result = data;

    double lower_bound = lower_bound_pct / 100.0;
    double upper_bound = upper_bound_pct / 100.0;

    auto valid_mask = (rank_data >= lower_bound) && (rank_data <= upper_bound);
    result = xt::where(valid_mask, result, std::numeric_limits<double>::quiet_NaN());

    return result;
}

// 面板中心化排名
template<typename T>
auto pn_RankCentered(const T& data) {
    auto rank_data = pn_Rank(data);
    return rank_data * 2.0 - 1.0;
}

// 面板最大值填充
template<typename T>
auto pn_FillMax(const T& data) {
    auto result = xt::empty_like(data);

    for (std::size_t i = 0; i < data.shape(0); ++i) {
        auto row = xt::view(data, i, xt::all());
        auto row_max = xt::nanmax(row)();
        xt::view(result, i, xt::all()) = row_max;
    }

    return result;
}

// 面板最小值填充
template<typename T>
auto pn_FillMin(const T& data) {
    auto result = xt::empty_like(data);

    for (std::size_t i = 0; i < data.shape(0); ++i) {
        auto row = xt::view(data, i, xt::all());
        auto row_min = xt::nanmin(row)();
        xt::view(result, i, xt::all()) = row_min;
    }

    return result;
}

// 面板Winsor化：限制异常值
template<typename T>
auto pn_Winsor(const T& data, double multiplier) {
    if (multiplier > 10.0) multiplier = 10.0;

    auto result = data;

    for (std::size_t i = 0; i < data.shape(0); ++i) {
        auto row = xt::view(data, i, xt::all());
        auto row_result = xt::view(result, i, xt::all());

        auto row_std = xt::nanstd(row)();
        double threshold = row_std * multiplier;

        for (std::size_t j = 0; j < row.size(); ++j) {
            if (!std::isnan(row(j))) {
                if (row(j) > 0 && row(j) > threshold) {
                    row_result(j) = threshold;
                } else if (row(j) < 0 && row(j) < -threshold) {
                    row_result(j) = -threshold;
                }
            }
        }
    }

    return result;
}

// 面板标准化
template<typename T>
auto pn_TransStd(const T& data) {
    auto result = xt::empty_like(data);

    for (std::size_t i = 0; i < data.shape(0); ++i) {
        auto row = xt::view(data, i, xt::all());
        auto row_result = xt::view(result, i, xt::all());

        auto row_mean = xt::nanmean(row)();
        auto row_std = xt::nanstd(row)();

        row_result = (row - row_mean) / row_std;
    }

    return result;
}

// 面板标准化（使用中位数）
template<typename T>
auto pn_Stand(const T& data) {
    auto result = xt::empty_like(data);

    for (std::size_t i = 0; i < data.shape(0); ++i) {
        auto row = xt::view(data, i, xt::all());
        auto row_result = xt::view(result, i, xt::all());

        // 计算中位数
        std::vector<double> valid_values;
        for (std::size_t j = 0; j < row.size(); ++j) {
            if (!std::isnan(row(j))) {
                valid_values.push_back(row(j));
            }
        }

        if (valid_values.empty()) {
            row_result.fill(std::numeric_limits<double>::quiet_NaN());
            continue;
        }

        std::sort(valid_values.begin(), valid_values.end());
        double median;
        if (valid_values.size() % 2 == 0) {
            median = (valid_values[valid_values.size()/2 - 1] + valid_values[valid_values.size()/2]) / 2.0;
        } else {
            median = valid_values[valid_values.size()/2];
        }

        auto row_std = xt::nanstd(row)();
        row_result = (row - median) / row_std;
    }

    return result;
}

// 面板交叉拟合
template<typename T1, typename T2>
auto pn_CrossFit(const T1& x, const T2& y) {
    auto result = xt::empty_like(y);

    for (std::size_t i = 0; i < x.shape(0); ++i) {
        auto x_row = xt::view(x, i, xt::all());
        auto y_row = xt::view(y, i, xt::all());
        auto result_row = xt::view(result, i, xt::all());

        auto x_mean = xt::nanmean(x_row)();
        auto y_mean = xt::nanmean(y_row)();

        // 计算回归系数
        auto x_centered = x_row - x_mean;
        auto y_centered = y_row - y_mean;

        auto numerator = xt::nansum(x_centered * y_centered)();
        auto denominator = xt::nansum(x_centered * x_centered)();

        if (denominator != 0) {
            double beta = numerator / denominator;
            double alpha = y_mean - beta * x_mean;

            auto y_fitted = alpha + beta * x_row;
            result_row = y_row - y_fitted;
        } else {
            result_row.fill(std::numeric_limits<double>::quiet_NaN());
        }
    }

    return result;
}

} // namespace feature_operators_xt

#endif // FEATURE_OPERATORS_XT_PANEL_OPS_HPP
