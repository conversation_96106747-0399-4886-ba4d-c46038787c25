#ifndef FEATURE_OPERATORS_XT_SIMPLE_PANEL_OPS_HPP
#define FEATURE_OPERATORS_XT_SIMPLE_PANEL_OPS_HPP

#include "feature_operators_xt/types.hpp"
#include <xtensor/xarray.hpp>
#include <xtensor/xmath.hpp>
#include <xtensor/xview.hpp>
#include <xtensor/xadapt.hpp>
#include <xtensor/xreducer.hpp>
#include <limits>
#include <cmath>

namespace feature_operators_xt {

// 辅助函数：计算忽略NaN的均值
template<typename T>
double compute_nanmean(const T& data) {
    double sum = 0.0;
    std::size_t count = 0;
    
    for (auto it = data.begin(); it != data.end(); ++it) {
        if (!std::isnan(*it)) {
            sum += *it;
            count++;
        }
    }
    
    return count > 0 ? sum / count : std::numeric_limits<double>::quiet_NaN();
}

// 辅助函数：计算忽略NaN的最大值
template<typename T>
double compute_nanmax(const T& data) {
    double max_val = -std::numeric_limits<double>::infinity();
    bool found_valid = false;
    
    for (auto it = data.begin(); it != data.end(); ++it) {
        if (!std::isnan(*it)) {
            if (!found_valid || *it > max_val) {
                max_val = *it;
                found_valid = true;
            }
        }
    }
    
    return found_valid ? max_val : std::numeric_limits<double>::quiet_NaN();
}

// 辅助函数：计算忽略NaN的最小值
template<typename T>
double compute_nanmin(const T& data) {
    double min_val = std::numeric_limits<double>::infinity();
    bool found_valid = false;
    
    for (auto it = data.begin(); it != data.end(); ++it) {
        if (!std::isnan(*it)) {
            if (!found_valid || *it < min_val) {
                min_val = *it;
                found_valid = true;
            }
        }
    }
    
    return found_valid ? min_val : std::numeric_limits<double>::quiet_NaN();
}

// 面板均值：计算每行的均值并复制到整行
template<typename T>
auto pn_Mean(const T& data) {
    auto result = xt::empty_like(data);
    
    for (std::size_t i = 0; i < data.shape(0); ++i) {
        auto row = xt::view(data, i, xt::all());
        auto row_mean = compute_nanmean(row);
        xt::view(result, i, xt::all()) = row_mean;
    }
    
    return result;
}

// 面板最大值填充
template<typename T>
auto pn_FillMax(const T& data) {
    auto result = xt::empty_like(data);
    
    for (std::size_t i = 0; i < data.shape(0); ++i) {
        auto row = xt::view(data, i, xt::all());
        auto row_max = compute_nanmax(row);
        xt::view(result, i, xt::all()) = row_max;
    }
    
    return result;
}

// 面板最小值填充
template<typename T>
auto pn_FillMin(const T& data) {
    auto result = xt::empty_like(data);
    
    for (std::size_t i = 0; i < data.shape(0); ++i) {
        auto row = xt::view(data, i, xt::all());
        auto row_min = compute_nanmin(row);
        xt::view(result, i, xt::all()) = row_min;
    }
    
    return result;
}

} // namespace feature_operators_xt

#endif // FEATURE_OPERATORS_XT_SIMPLE_PANEL_OPS_HPP
