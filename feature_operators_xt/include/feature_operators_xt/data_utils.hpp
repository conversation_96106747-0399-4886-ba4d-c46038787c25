#ifndef FEATURE_OPERATORS_XT_DATA_UTILS_HPP
#define FEATURE_OPERATORS_XT_DATA_UTILS_HPP

#include "feature_operators_xt/types.hpp"
#include <xtensor/xarray.hpp>
#include <xtensor/xmath.hpp>
#include <xtensor/xview.hpp>
#include <xtensor/xadapt.hpp>
#include <limits>
#include <cmath>

namespace feature_operators_xt {

// 过滤无穷值 - 严格按照Python版本逻辑
template<typename T>
auto FilterInf(const T& s1) {
    // Python逻辑：
    // data = s1.copy()
    // data.replace(np.inf, np.nan, inplace=True)
    // return data
    auto data = s1;
    auto inf_mask = xt::isinf(data);
    data = xt::where(inf_mask, std::numeric_limits<double>::quiet_NaN(), data);
    return data;
}

// 填充NaN值 - 严格按照Python版本逻辑
template<typename T1, typename T2>
auto FillNan(const T1& data, const T2& fillData_) {
    // Python逻辑：
    // newData = data.copy()
    // fillData = fillData_.copy()
    // if (type(fillData) == int) | (type(fillData) == float):
    //     newData[np.isnan(newData)] = fillData
    // else:
    //     assert data.shape == fillData.shape, 'Shape Mismatch!'
    //     newData[np.isnan(newData)] = fillData[np.isnan(newData)]
    // return newData
    auto newData = data;
    auto fillData = fillData_;
    auto nan_mask = xt::isnan(newData);
    newData = xt::where(nan_mask, fillData, newData);
    return newData;
}

// 获取与输入相同形状的NaN数组 - 严格按照Python版本逻辑
template<typename T>
auto getNan(const T& s1) {
    // Python逻辑：
    // data = s1.copy()
    // isNpArray = isinstance(data, np.ndarray)
    // if isNpArray:
    //     return np.full(data.shape, np.nan)
    // else:
    //     return pd.DataFrame(np.full(data.shape, np.nan), index=data.index, columns=data.columns)
    auto data = s1;
    auto result = xt::empty_like(data);
    result.fill(std::numeric_limits<double>::quiet_NaN());
    return result;
}

// 获取与输入相同形状的Inf数组 - 严格按照Python版本逻辑
template<typename T>
auto getInf(const T& s1) {
    // Python逻辑：
    // data = s1.copy()
    // isNpArray = isinstance(data, np.ndarray)
    // if isNpArray:
    //     return np.full(data.shape, np.inf)
    // else:
    //     return pd.DataFrame(np.full(data.shape, np.inf), index=data.index, columns=data.columns)
    auto data = s1;
    auto result = xt::empty_like(data);
    result.fill(std::numeric_limits<double>::infinity());
    return result;
}

} // namespace feature_operators_xt

#endif // FEATURE_OPERATORS_XT_DATA_UTILS_HPP
