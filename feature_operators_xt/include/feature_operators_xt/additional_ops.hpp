#ifndef FEATURE_OPERATORS_XT_ADDITIONAL_OPS_HPP
#define FEATURE_OPERATORS_XT_ADDITIONAL_OPS_HPP

#include "feature_operators_xt/types.hpp"
#include <xtensor/xarray.hpp>
#include <xtensor/xmath.hpp>
#include <xtensor/xview.hpp>
#include <xtensor/xadapt.hpp>
#include <xtensor/xreducer.hpp>
#include <limits>
#include <cmath>
#include <algorithm>

namespace feature_operators_xt {

// 面板截断 - 严格按照Python版本逻辑
template<typename T>
auto pn_Cut(const T& s1) {
    // Python逻辑：
    // UpBound = 80
    // LowBound = 20
    // data = s1.copy()
    // UpBound = UpBound / 100
    // LowBound = LowBound / 100
    // RankMT = pn_Rank(data)
    // ValidFilter = (RankMT >= LowBound) & (RankMT <= UpBound)
    // newData = data.copy()
    // newData[~ValidFilter] = np.nan
    // return newData
    
    double UpBound = 80.0;
    double LowBound = 20.0;
    auto data = s1;
    UpBound = UpBound / 100.0;
    LowBound = LowBound / 100.0;
    auto RankMT = pn_Rank(data);
    auto ValidFilter = (RankMT >= LowBound) && (RankMT <= UpBound);
    auto newData = data;
    newData = xt::where(ValidFilter, newData, std::numeric_limits<double>::quiet_NaN());
    return newData;
}

// 面板Winsor化 - 严格按照Python版本逻辑
template<typename T>
auto pn_Winsor(const T& s1, double Multiplier) {
    // Python逻辑：
    // if Multiplier > 10:
    //     Multiplier = 10
    // data = s1.copy()
    // isPdFrame = isinstance(data, pd.DataFrame)
    // if isPdFrame:
    //     index = data.index
    //     columns = data.columns
    //     data = data.values
    // panelStd = np.nanstd(data, ddof=1, axis=1)
    // thresHold = _repmat(panelStd, data.shape[1], axis=1) * Multiplier
    // newData = data.copy()
    // # 若原始數據為正，且超過正thresHold，以正thresHold代替
    // if any(sum((data > 0) & (data > thresHold))):
    //     newData[(data > 0) & (data > thresHold)] = thresHold[(data > 0) & (data > thresHold)]
    // # 若原始數據為負，且超過負thresHold，以負thresHold代替
    // if any(sum((data < 0) & (data < -thresHold))):
    //     newData[(data < 0) & (data < -thresHold)] = -thresHold[(data < 0) & (data < -thresHold)]
    // if isPdFrame:
    //     newData = pd.DataFrame(newData, index=index, columns=columns)
    // return newData
    
    if (Multiplier > 10.0) {
        Multiplier = 10.0;
    }
    
    auto data = s1;
    auto newData = data;
    
    for (std::size_t i = 0; i < data.shape(0); ++i) {
        auto row = xt::view(data, i, xt::all());
        auto result_row = xt::view(newData, i, xt::all());
        
        // 计算该行的标准差（忽略NaN）
        std::vector<double> valid_values;
        for (auto it = row.begin(); it != row.end(); ++it) {
            if (!std::isnan(*it)) {
                valid_values.push_back(*it);
            }
        }
        
        if (valid_values.size() <= 1) {
            continue;
        }
        
        double mean = 0.0;
        for (double val : valid_values) {
            mean += val;
        }
        mean /= valid_values.size();
        
        double variance = 0.0;
        for (double val : valid_values) {
            variance += (val - mean) * (val - mean);
        }
        variance /= (valid_values.size() - 1);  // ddof=1
        double std_dev = std::sqrt(variance);
        
        double threshold = std_dev * Multiplier;
        
        // 应用Winsor化
        for (std::size_t j = 0; j < row.size(); ++j) {
            if (!std::isnan(row(j))) {
                if (row(j) > 0 && row(j) > threshold) {
                    result_row(j) = threshold;
                } else if (row(j) < 0 && row(j) < -threshold) {
                    result_row(j) = -threshold;
                }
            }
        }
    }
    
    return newData;
}

// 面板标准化 - 严格按照Python版本逻辑
template<typename T>
auto pn_TransStd(const T& s1) {
    // Python逻辑：
    // data = s1.copy()
    // isPdFrame = isinstance(data, pd.DataFrame)
    // if isPdFrame:
    //     index = data.index
    //     columns = data.columns
    //     data = data.values
    // meanData = _repmat(np.nanmean(data, axis=1), data.shape[1], axis=1)
    // stdData = _repmat(np.nanstd(data, ddof=1, axis=1), data.shape[1], axis=1)
    // newData = np.divide((data - meanData), stdData)
    // if isPdFrame:
    //     newData = pd.DataFrame(newData, index=index, columns=columns)
    // return newData
    
    auto data = s1;
    auto result = xt::empty_like(data);
    
    for (std::size_t i = 0; i < data.shape(0); ++i) {
        auto row = xt::view(data, i, xt::all());
        auto result_row = xt::view(result, i, xt::all());
        
        // 计算该行的均值和标准差（忽略NaN）
        std::vector<double> valid_values;
        for (auto it = row.begin(); it != row.end(); ++it) {
            if (!std::isnan(*it)) {
                valid_values.push_back(*it);
            }
        }
        
        if (valid_values.size() <= 1) {
            result_row.fill(std::numeric_limits<double>::quiet_NaN());
            continue;
        }
        
        double mean = 0.0;
        for (double val : valid_values) {
            mean += val;
        }
        mean /= valid_values.size();
        
        double variance = 0.0;
        for (double val : valid_values) {
            variance += (val - mean) * (val - mean);
        }
        variance /= (valid_values.size() - 1);  // ddof=1
        double std_dev = std::sqrt(variance);
        
        // 标准化
        for (std::size_t j = 0; j < row.size(); ++j) {
            if (!std::isnan(row(j))) {
                result_row(j) = (row(j) - mean) / std_dev;
            } else {
                result_row(j) = std::numeric_limits<double>::quiet_NaN();
            }
        }
    }
    
    return result;
}

// 面板交叉拟合 - 严格按照Python版本逻辑
template<typename T1, typename T2>
auto pn_CrossFit(const T1& s1, const T2& s2) {
    // Python逻辑：
    // x = s1.copy()
    // y = s2.copy()
    // b =( x.sub(x.mean(1),axis = 0) * y.sub(y.mean(1),axis = 0)).sum(1) / (x.sub(x.mean(1),axis = 0) * x.sub(x.mean(1),axis = 0)).sum(1)
    // a = y.mean(1) - b * x.mean(1)
    // res = y - (Repmat(x, a) + Repmat(x, b) * x)
    // return res
    
    auto x = s1;
    auto y = s2;
    auto result = xt::empty_like(y);
    
    for (std::size_t i = 0; i < x.shape(0); ++i) {
        auto x_row = xt::view(x, i, xt::all());
        auto y_row = xt::view(y, i, xt::all());
        auto result_row = xt::view(result, i, xt::all());
        
        // 计算x和y的均值
        double x_mean = 0.0, y_mean = 0.0;
        std::size_t count = 0;
        
        for (std::size_t j = 0; j < x_row.size(); ++j) {
            if (!std::isnan(x_row(j)) && !std::isnan(y_row(j))) {
                x_mean += x_row(j);
                y_mean += y_row(j);
                count++;
            }
        }
        
        if (count == 0) {
            result_row.fill(std::numeric_limits<double>::quiet_NaN());
            continue;
        }
        
        x_mean /= count;
        y_mean /= count;
        
        // 计算回归系数
        double numerator = 0.0, denominator = 0.0;
        for (std::size_t j = 0; j < x_row.size(); ++j) {
            if (!std::isnan(x_row(j)) && !std::isnan(y_row(j))) {
                double x_centered = x_row(j) - x_mean;
                double y_centered = y_row(j) - y_mean;
                numerator += x_centered * y_centered;
                denominator += x_centered * x_centered;
            }
        }
        
        if (denominator == 0.0) {
            result_row.fill(std::numeric_limits<double>::quiet_NaN());
            continue;
        }
        
        double b = numerator / denominator;
        double a = y_mean - b * x_mean;
        
        // 计算残差
        for (std::size_t j = 0; j < x_row.size(); ++j) {
            if (!std::isnan(x_row(j)) && !std::isnan(y_row(j))) {
                double y_fitted = a + b * x_row(j);
                result_row(j) = y_row(j) - y_fitted;
            } else {
                result_row(j) = std::numeric_limits<double>::quiet_NaN();
            }
        }
    }
    
    return result;
}

// 时间序列标准差 - 严格按照Python版本逻辑
template<typename T>
auto ts_Stdev(const T& s1, int n) {
    // Python逻辑：
    // n = int(n)
    // if n <= 1:
    //     n = 2
    // tem = s1.copy()
    // tem1 = tem.rolling(n, axis=0, min_periods=1).std()
    // return tem1
    
    n = static_cast<int>(n);
    if (n <= 1) {
        n = 2;
    }
    
    auto tem = s1;
    auto result = xt::empty_like(tem);
    result.fill(std::numeric_limits<double>::quiet_NaN());
    
    for (std::size_t i = 0; i < tem.shape(0); ++i) {
        for (std::size_t j = 0; j < tem.shape(1); ++j) {
            std::vector<double> valid_values;
            
            // 计算滚动窗口的起始位置
            std::size_t start = (i >= static_cast<std::size_t>(n-1)) ? i - n + 1 : 0;
            
            for (std::size_t k = start; k <= i; ++k) {
                if (!std::isnan(tem(k, j))) {
                    valid_values.push_back(tem(k, j));
                }
            }
            
            if (valid_values.size() > 1) {
                double mean = 0.0;
                for (double val : valid_values) {
                    mean += val;
                }
                mean /= valid_values.size();
                
                double variance = 0.0;
                for (double val : valid_values) {
                    variance += (val - mean) * (val - mean);
                }
                variance /= (valid_values.size() - 1);
                
                result(i, j) = std::sqrt(variance);
            }
        }
    }
    
    return result;
}

// 时间序列差分 - 严格按照Python版本逻辑
template<typename T>
auto ts_Delta(const T& s1, int n) {
    // Python逻辑：
    // n = int(n)
    // tem = s1.copy()
    // tem1 = tem-tem.shift(n, axis=0)
    // return tem1
    
    n = static_cast<int>(n);
    auto tem = s1;
    auto delayed = ts_Delay(tem, n);
    return tem - delayed;
}

} // namespace feature_operators_xt

#endif // FEATURE_OPERATORS_XT_ADDITIONAL_OPS_HPP
