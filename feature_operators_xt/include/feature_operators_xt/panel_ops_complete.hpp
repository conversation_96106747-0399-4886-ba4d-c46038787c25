#ifndef FEATURE_OPERATORS_XT_PANEL_OPS_COMPLETE_HPP
#define FEATURE_OPERATORS_XT_PANEL_OPS_COMPLETE_HPP

#include "feature_operators_xt/types.hpp"
#include <xtensor/xarray.hpp>
#include <xtensor/xmath.hpp>
#include <xtensor/xview.hpp>
#include <xtensor/xadapt.hpp>
#include <xtensor/xreducer.hpp>
#include <limits>
#include <cmath>
#include <algorithm>

namespace feature_operators_xt {

// 辅助函数：复制数据到每一行
template<typename T>
auto _repmat(const T& data, std::size_t repeat_num, int axis = 1) {
    auto result = xt::empty<double>({data.size(), repeat_num});
    
    if (axis == 1) {
        // 沿列方向复制
        for (std::size_t i = 0; i < data.size(); ++i) {
            for (std::size_t j = 0; j < repeat_num; ++j) {
                result(i, j) = data(i);
            }
        }
    }
    
    return result;
}

// 面板均值 - 严格按照Python版本逻辑
template<typename T>
auto pn_Mean(const T& s1) {
    // Python逻辑：
    // data = s1.copy()
    // isPdFrame = isinstance(data, pd.DataFrame)
    // newData = _repmat(np.nanmean(data, axis=1), data.shape[1], axis=1)
    // if isPdFrame:
    //     newData = pd.DataFrame(newData, index=data.index, columns=data.columns)
    // return newData
    
    auto data = s1;
    auto result = xt::empty_like(data);
    
    for (std::size_t i = 0; i < data.shape(0); ++i) {
        auto row = xt::view(data, i, xt::all());
        
        // 计算该行的均值（忽略NaN）
        double sum = 0.0;
        std::size_t count = 0;
        for (auto it = row.begin(); it != row.end(); ++it) {
            if (!std::isnan(*it)) {
                sum += *it;
                count++;
            }
        }
        
        double row_mean = (count > 0) ? sum / count : std::numeric_limits<double>::quiet_NaN();
        
        // 将均值复制到整行
        xt::view(result, i, xt::all()) = row_mean;
    }
    
    return result;
}

// 面板排序 - 严格按照Python版本逻辑
template<typename T>
auto pn_Rank(const T& s2) {
    // Python逻辑：
    // s1 = s2.copy()
    // rank_ = s1.rank(pct=True, axis=1)
    // cut = rank_.min(axis=1) / 2
    // rank_ = rank_.sub(cut, axis=0)
    // return rank_
    
    auto s1 = s2;
    auto result = xt::empty_like(s1);
    
    for (std::size_t i = 0; i < s1.shape(0); ++i) {
        auto row = xt::view(s1, i, xt::all());
        auto result_row = xt::view(result, i, xt::all());
        
        // 收集有效值和索引
        std::vector<std::pair<double, std::size_t>> valid_pairs;
        for (std::size_t j = 0; j < row.size(); ++j) {
            if (!std::isnan(row(j))) {
                valid_pairs.push_back({row(j), j});
            }
        }
        
        if (valid_pairs.empty()) {
            result_row.fill(std::numeric_limits<double>::quiet_NaN());
            continue;
        }
        
        // 排序
        std::sort(valid_pairs.begin(), valid_pairs.end());
        
        // 计算百分位排名
        for (std::size_t k = 0; k < valid_pairs.size(); ++k) {
            std::size_t idx = valid_pairs[k].second;
            double pct_rank = static_cast<double>(k + 1) / valid_pairs.size();
            result_row(idx) = pct_rank;
        }
        
        // 设置NaN值
        for (std::size_t j = 0; j < row.size(); ++j) {
            if (std::isnan(row(j))) {
                result_row(j) = std::numeric_limits<double>::quiet_NaN();
            }
        }
        
        // 计算cut值并调整
        double min_rank = 1.0 / (2.0 * valid_pairs.size());
        for (std::size_t j = 0; j < row.size(); ++j) {
            if (!std::isnan(result_row(j))) {
                result_row(j) -= min_rank;
            }
        }
    }
    
    return result;
}

// 面板排序2 - 严格按照Python版本逻辑
template<typename T>
auto pn_Rank2(const T& s1) {
    // Python逻辑：
    // data = s1.copy()
    // newData = data.rank(axis=1)
    // return newData
    
    auto data = s1;
    auto result = xt::empty_like(data);
    
    for (std::size_t i = 0; i < data.shape(0); ++i) {
        auto row = xt::view(data, i, xt::all());
        auto result_row = xt::view(result, i, xt::all());
        
        // 收集有效值和索引
        std::vector<std::pair<double, std::size_t>> valid_pairs;
        for (std::size_t j = 0; j < row.size(); ++j) {
            if (!std::isnan(row(j))) {
                valid_pairs.push_back({row(j), j});
            }
        }
        
        if (valid_pairs.empty()) {
            result_row.fill(std::numeric_limits<double>::quiet_NaN());
            continue;
        }
        
        // 排序
        std::sort(valid_pairs.begin(), valid_pairs.end());
        
        // 分配排名（从1开始）
        for (std::size_t k = 0; k < valid_pairs.size(); ++k) {
            std::size_t idx = valid_pairs[k].second;
            result_row(idx) = k + 1;
        }
        
        // 设置NaN值
        for (std::size_t j = 0; j < row.size(); ++j) {
            if (std::isnan(row(j))) {
                result_row(j) = std::numeric_limits<double>::quiet_NaN();
            }
        }
    }
    
    return result;
}

// 面板中心化排名 - 严格按照Python版本逻辑
template<typename T>
auto pn_RankCentered(const T& s1) {
    // Python逻辑：
    // data = s1.copy()
    // newData = _pn_Rank(data)
    // newData = newData * 2 - 1
    // return newData
    
    auto data = s1;
    auto newData = pn_Rank(data);
    return newData * 2.0 - 1.0;
}

// 面板最大值填充 - 严格按照Python版本逻辑
template<typename T>
auto pn_FillMax(const T& s1) {
    // Python逻辑：
    // data = s1.copy()
    // isPdFrame = isinstance(data, pd.DataFrame)
    // if isPdFrame:
    //     index = data.index
    //     columns = data.columns
    //     data = data.values
    // newData = _repmat(np.nanmax(data, axis=1), data.shape[1], axis=1)
    // if isPdFrame:
    //     newData = pd.DataFrame(newData, index=index, columns=columns)
    // return newData
    
    auto data = s1;
    auto result = xt::empty_like(data);
    
    for (std::size_t i = 0; i < data.shape(0); ++i) {
        auto row = xt::view(data, i, xt::all());
        
        // 计算该行的最大值（忽略NaN）
        double max_val = -std::numeric_limits<double>::infinity();
        bool found_valid = false;
        for (auto it = row.begin(); it != row.end(); ++it) {
            if (!std::isnan(*it)) {
                if (!found_valid || *it > max_val) {
                    max_val = *it;
                    found_valid = true;
                }
            }
        }
        
        double row_max = found_valid ? max_val : std::numeric_limits<double>::quiet_NaN();
        
        // 将最大值复制到整行
        xt::view(result, i, xt::all()) = row_max;
    }
    
    return result;
}

// 面板最小值填充 - 严格按照Python版本逻辑
template<typename T>
auto pn_FillMin(const T& s1) {
    // Python逻辑：
    // data = s1.copy()
    // isPdFrame = isinstance(data, pd.DataFrame)
    // if isPdFrame:
    //     index = data.index
    //     columns = data.columns
    //     data = data.values
    // newData = _repmat(np.nanmin(data, axis=1), data.shape[1], axis=1)
    // if isPdFrame:
    //     newData = pd.DataFrame(newData, index=index, columns=columns)
    // return newData
    
    auto data = s1;
    auto result = xt::empty_like(data);
    
    for (std::size_t i = 0; i < data.shape(0); ++i) {
        auto row = xt::view(data, i, xt::all());
        
        // 计算该行的最小值（忽略NaN）
        double min_val = std::numeric_limits<double>::infinity();
        bool found_valid = false;
        for (auto it = row.begin(); it != row.end(); ++it) {
            if (!std::isnan(*it)) {
                if (!found_valid || *it < min_val) {
                    min_val = *it;
                    found_valid = true;
                }
            }
        }
        
        double row_min = found_valid ? min_val : std::numeric_limits<double>::quiet_NaN();
        
        // 将最小值复制到整行
        xt::view(result, i, xt::all()) = row_min;
    }
    
    return result;
}

} // namespace feature_operators_xt

#endif // FEATURE_OPERATORS_XT_PANEL_OPS_COMPLETE_HPP
