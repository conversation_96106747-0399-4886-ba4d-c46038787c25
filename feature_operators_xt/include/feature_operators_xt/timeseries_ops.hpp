#ifndef FEATURE_OPERATORS_XT_TIMESERIES_OPS_HPP
#define FEATURE_OPERATORS_XT_TIMESERIES_OPS_HPP

#include "feature_operators_xt/types.hpp"
#include <xtensor/xarray.hpp>
#include <xtensor/xmath.hpp>
#include <xtensor/xview.hpp>
#include <xtensor/xadapt.hpp>
#include <xtensor/xreducer.hpp>
#include <limits>
#include <cmath>
#include <algorithm>
#include <vector>

namespace feature_operators_xt {

// 时间序列延迟 - 严格按照Python版本逻辑
template<typename T>
auto ts_Delay(const T& s1, int n) {
    // Python逻辑：
    // n = int(n)
    // tem = s1.copy()
    // tem1 = tem.shift(n, axis=0)
    // return tem1
    n = static_cast<int>(n);
    auto tem = s1;
    auto result = xt::empty_like(tem);
    result.fill(std::numeric_limits<double>::quiet_NaN());

    if (n > 0) {
        // 正向延迟：将数据向下移动n行
        if (n < static_cast<int>(tem.shape(0))) {
            xt::view(result, xt::range(n, xt::placeholders::_), xt::all()) =
                xt::view(tem, xt::range(0, tem.shape(0) - n), xt::all());
        }
    } else if (n < 0) {
        // 负向延迟：将数据向上移动|n|行
        int abs_n = -n;
        if (abs_n < static_cast<int>(tem.shape(0))) {
            xt::view(result, xt::range(0, tem.shape(0) - abs_n), xt::all()) =
                xt::view(tem, xt::range(abs_n, xt::placeholders::_), xt::all());
        }
    } else {
        // n == 0，返回原数据
        result = tem;
    }

    return result;
}

// 时间序列均值 - 严格按照Python版本逻辑
template<typename T>
auto ts_Mean(const T& s1, int n) {
    // Python逻辑：
    // n = int(n)
    // if n == 0:
    //     return s1
    // tem = s1.copy()
    // tem1 = tem.rolling(n, axis=0, min_periods=1).mean()
    // return tem1
    n = static_cast<int>(n);
    if (n == 0) {
        return s1;
    }

    auto tem = s1;
    auto result = xt::empty_like(tem);
    result.fill(std::numeric_limits<double>::quiet_NaN());

    for (std::size_t i = 0; i < tem.shape(0); ++i) {
        for (std::size_t j = 0; j < tem.shape(1); ++j) {
            double sum = 0.0;
            std::size_t count = 0;

            // 计算滚动窗口的起始位置
            std::size_t start = (i >= static_cast<std::size_t>(n-1)) ? i - n + 1 : 0;

            for (std::size_t k = start; k <= i; ++k) {
                if (!std::isnan(tem(k, j))) {
                    sum += tem(k, j);
                    count++;
                }
            }

            if (count > 0) {
                result(i, j) = sum / count;
            }
        }
    }

    return result;
}

// 时间序列标准差
template<typename T>
auto ts_Stdev(const T& data, int n) {
    if (n <= 1) n = 2;

    auto result = xt::empty_like(data);
    result.fill(std::numeric_limits<double>::quiet_NaN());

    for (std::size_t i = 0; i < data.shape(0); ++i) {
        std::size_t start = (i >= static_cast<std::size_t>(n-1)) ? i - n + 1 : 0;

        for (std::size_t j = 0; j < data.shape(1); ++j) {
            std::vector<double> valid_values;

            for (std::size_t k = start; k <= i; ++k) {
                if (!std::isnan(data(k, j))) {
                    valid_values.push_back(data(k, j));
                }
            }

            if (valid_values.size() > 1) {
                double mean = 0.0;
                for (double val : valid_values) {
                    mean += val;
                }
                mean /= valid_values.size();

                double variance = 0.0;
                for (double val : valid_values) {
                    variance += (val - mean) * (val - mean);
                }
                variance /= (valid_values.size() - 1);

                result(i, j) = std::sqrt(variance);
            }
        }
    }

    return result;
}

// 时间序列差分
template<typename T>
auto ts_Delta(const T& data, int n) {
    auto delayed = ts_Delay(data, n);
    return data - delayed;
}

// 时间序列最小值
template<typename T>
auto ts_Min(const T& data, int n) {
    if (n <= 0) return data;

    auto result = xt::empty_like(data);
    result.fill(std::numeric_limits<double>::quiet_NaN());

    for (std::size_t i = 0; i < data.shape(0); ++i) {
        std::size_t start = (i >= static_cast<std::size_t>(n-1)) ? i - n + 1 : 0;

        for (std::size_t j = 0; j < data.shape(1); ++j) {
            double min_val = std::numeric_limits<double>::infinity();
            bool found_valid = false;

            for (std::size_t k = start; k <= i; ++k) {
                if (!std::isnan(data(k, j))) {
                    if (!found_valid || data(k, j) < min_val) {
                        min_val = data(k, j);
                        found_valid = true;
                    }
                }
            }

            if (found_valid) {
                result(i, j) = min_val;
            }
        }
    }

    return result;
}

// 时间序列最大值
template<typename T>
auto ts_Max(const T& data, int n) {
    if (n <= 0) return data;

    auto result = xt::empty_like(data);
    result.fill(std::numeric_limits<double>::quiet_NaN());

    for (std::size_t i = 0; i < data.shape(0); ++i) {
        std::size_t start = (i >= static_cast<std::size_t>(n-1)) ? i - n + 1 : 0;

        for (std::size_t j = 0; j < data.shape(1); ++j) {
            double max_val = -std::numeric_limits<double>::infinity();
            bool found_valid = false;

            for (std::size_t k = start; k <= i; ++k) {
                if (!std::isnan(data(k, j))) {
                    if (!found_valid || data(k, j) > max_val) {
                        max_val = data(k, j);
                        found_valid = true;
                    }
                }
            }

            if (found_valid) {
                result(i, j) = max_val;
            }
        }
    }

    return result;
}

// 时间序列求和
template<typename T>
auto ts_Sum(const T& data, int n) {
    if (n <= 0) return data;

    auto result = xt::empty_like(data);
    result.fill(std::numeric_limits<double>::quiet_NaN());

    for (std::size_t i = 0; i < data.shape(0); ++i) {
        std::size_t start = (i >= static_cast<std::size_t>(n-1)) ? i - n + 1 : 0;

        for (std::size_t j = 0; j < data.shape(1); ++j) {
            double sum = 0.0;
            bool found_valid = false;

            for (std::size_t k = start; k <= i; ++k) {
                if (!std::isnan(data(k, j))) {
                    sum += data(k, j);
                    found_valid = true;
                }
            }

            if (found_valid) {
                result(i, j) = sum;
            }
        }
    }

    return result;
}

// 时间序列变化率
template<typename T>
auto ts_ChgRate(const T& data, int n) {
    if (n < 1) n = 1;

    auto delayed = ts_Delay(data, n);
    auto ratio = data / delayed;
    return ratio - 1.0;
}

// 时间序列除法
template<typename T>
auto ts_Divide(const T& data, int n) {
    if (n < 1) n = 1;

    auto delayed = ts_Delay(data, n);
    return data / delayed;
}

} // namespace feature_operators_xt

#endif // FEATURE_OPERATORS_XT_TIMESERIES_OPS_HPP
