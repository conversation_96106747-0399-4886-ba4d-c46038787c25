#ifndef FEATURE_OPERATORS_XT_CORE_MATH_HPP
#define FEATURE_OPERATORS_XT_CORE_MATH_HPP

#include "feature_operators_xt/types.hpp"
#include <xtensor/xarray.hpp>
#include <xtensor/xmath.hpp>
#include <xtensor/xview.hpp>
#include <xtensor/xadapt.hpp>
#include <cmath>
#include <limits>

namespace feature_operators_xt {

// 基础二元运算 - 严格按照Python版本实现
template<typename T1, typename T2>
auto Add(const T1& s1, const T2& s2) {
    return s1 + s2;
}

template<typename T1, typename T2>
auto Minus(const T1& s1, const T2& s2) {
    return s1 - s2;
}

template<typename T1, typename T2>
auto Multiply(const T1& s1, const T2& s2) {
    return s1 * s2;
}

template<typename T1, typename T2>
auto Divide(const T1& s1, const T2& s2) {
    return s1 / s2;
}

// 基础一元运算
template<typename T>
auto Sqrt(const T& s1) {
    // Python: return s1**0.5
    return xt::pow(s1, 0.5);
}

template<typename T>
auto log(const T& s1) {
    // Python: return np.log(s1)
    return xt::log(s1);
}

template<typename T>
auto inv(const T& s1) {
    // Python: return 1/s1
    return 1.0 / s1;
}

template<typename T>
auto Floor(const T& data) {
    // Python: return np.floor(data)
    return xt::floor(data);
}

template<typename T>
auto Ceil(const T& data) {
    // Python: return np.floor(data) - 注意：原Python代码有错误，应该是np.ceil
    return xt::ceil(data);
}

template<typename T>
auto Round(const T& data) {
    // Python: return np.round(data)
    return xt::round(data);
}

template<typename T>
auto Abs(const T& data) {
    // Python: return abs(data)
    return xt::abs(data);
}

// Log函数 - 严格按照Python版本逻辑
template<typename T>
auto Log(const T& data) {
    // Python逻辑：
    // newData = data.copy()
    // newData[newData <= 0] = np.nan
    // return np.log(newData)
    auto newData = data;
    auto condition = newData <= 0.0;
    newData = xt::where(condition, std::numeric_limits<double>::quiet_NaN(), newData);
    return xt::log(newData);
}

template<typename T>
auto Sign(const T& data) {
    // Python: return np.sign(data)
    return xt::sign(data);
}

template<typename T>
auto Reverse(const T& data) {
    // Python: return data * -1
    return data * -1.0;
}

template<typename T>
auto Power(const T& s1, double n) {
    // Python: n = int(n); return np.power(s1, n)
    int int_n = static_cast<int>(n);
    return xt::pow(s1, int_n);
}

template<typename T>
auto Exp(const T& data) {
    // Python: return np.exp(data)
    return xt::exp(data);
}

// 带符号的幂运算 - 严格按照Python版本逻辑
template<typename T>
auto SignedPower(const T& s1, double Power) {
    // Python逻辑：
    // data = s1.copy()
    // negFilter = data < 0
    // newData = abs(data) ** Power
    // newData[negFilter] = -newData[negFilter]
    // return newData
    auto data = s1;
    auto negFilter = data < 0.0;
    auto newData = xt::pow(xt::abs(data), Power);
    newData = xt::where(negFilter, -newData, newData);
    return newData;
}

} // namespace feature_operators_xt

#endif // FEATURE_OPERATORS_XT_CORE_MATH_HPP
