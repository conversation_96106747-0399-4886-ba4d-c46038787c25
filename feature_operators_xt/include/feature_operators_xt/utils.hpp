#ifndef FEATURE_OPERATORS_XT_UTILS_HPP
#define FEATURE_OPERATORS_XT_UTILS_HPP

#include <xtensor/xarray.hpp>
#include <xtensor/xview.hpp>
#include <xtensor/xmath.hpp>
#include <vector>
#include <algorithm>
#include <cmath>
#include <limits>

namespace feature_operators_xt {
namespace utils {

// 计算忽略NaN的均值
template<typename T>
double nanmean(const T& data) {
    double sum = 0.0;
    std::size_t count = 0;
    
    for (auto it = data.begin(); it != data.end(); ++it) {
        if (!std::isnan(*it)) {
            sum += *it;
            count++;
        }
    }
    
    return count > 0 ? sum / count : std::numeric_limits<double>::quiet_NaN();
}

// 计算忽略NaN的标准差
template<typename T>
double nanstd(const T& data) {
    std::vector<double> valid_values;
    
    for (auto it = data.begin(); it != data.end(); ++it) {
        if (!std::isnan(*it)) {
            valid_values.push_back(*it);
        }
    }
    
    if (valid_values.size() <= 1) {
        return std::numeric_limits<double>::quiet_NaN();
    }
    
    double mean = 0.0;
    for (double val : valid_values) {
        mean += val;
    }
    mean /= valid_values.size();
    
    double variance = 0.0;
    for (double val : valid_values) {
        variance += (val - mean) * (val - mean);
    }
    variance /= (valid_values.size() - 1);
    
    return std::sqrt(variance);
}

// 计算忽略NaN的最大值
template<typename T>
double nanmax(const T& data) {
    double max_val = -std::numeric_limits<double>::infinity();
    bool found_valid = false;
    
    for (auto it = data.begin(); it != data.end(); ++it) {
        if (!std::isnan(*it)) {
            if (!found_valid || *it > max_val) {
                max_val = *it;
                found_valid = true;
            }
        }
    }
    
    return found_valid ? max_val : std::numeric_limits<double>::quiet_NaN();
}

// 计算忽略NaN的最小值
template<typename T>
double nanmin(const T& data) {
    double min_val = std::numeric_limits<double>::infinity();
    bool found_valid = false;
    
    for (auto it = data.begin(); it != data.end(); ++it) {
        if (!std::isnan(*it)) {
            if (!found_valid || *it < min_val) {
                min_val = *it;
                found_valid = true;
            }
        }
    }
    
    return found_valid ? min_val : std::numeric_limits<double>::quiet_NaN();
}

// 计算忽略NaN的和
template<typename T>
double nansum(const T& data) {
    double sum = 0.0;
    bool found_valid = false;
    
    for (auto it = data.begin(); it != data.end(); ++it) {
        if (!std::isnan(*it)) {
            sum += *it;
            found_valid = true;
        }
    }
    
    return found_valid ? sum : std::numeric_limits<double>::quiet_NaN();
}

// 计算中位数
template<typename T>
double nanmedian(const T& data) {
    std::vector<double> valid_values;
    
    for (auto it = data.begin(); it != data.end(); ++it) {
        if (!std::isnan(*it)) {
            valid_values.push_back(*it);
        }
    }
    
    if (valid_values.empty()) {
        return std::numeric_limits<double>::quiet_NaN();
    }
    
    std::sort(valid_values.begin(), valid_values.end());
    
    if (valid_values.size() % 2 == 0) {
        return (valid_values[valid_values.size()/2 - 1] + valid_values[valid_values.size()/2]) / 2.0;
    } else {
        return valid_values[valid_values.size()/2];
    }
}

} // namespace utils
} // namespace feature_operators_xt

#endif // FEATURE_OPERATORS_XT_UTILS_HPP
