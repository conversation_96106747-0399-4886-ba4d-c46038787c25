#ifndef FEATURE_OPERATORS_XT_COMPARISON_OPS_HPP
#define FEATURE_OPERATORS_XT_COMPARISON_OPS_HPP

#include "feature_operators_xt/types.hpp"
#include <xtensor/xarray.hpp>
#include <xtensor/xmath.hpp>
#include <xtensor/xview.hpp>
#include <xtensor/xadapt.hpp>
#include <xtensor/xnan_functions.hpp>

namespace feature_operators_xt {

// 大于比较：a > b 返回1，否则返回0
template<typename T1, typename T2>
auto Mthan(const T1& a, const T2& b) {
    auto result = xt::cast<double>(a > b);
    return result;
}

// 大于等于比较：a >= b 返回1，否则返回0
template<typename T1, typename T2>
auto MEthan(const T1& a, const T2& b) {
    auto result = xt::cast<double>(a >= b);
    return result;
}

// 小于比较：a < b 返回1，否则返回0
template<typename T1, typename T2>
auto Lthan(const T1& a, const T2& b) {
    auto result = xt::cast<double>(a < b);
    return result;
}

// 小于等于比较：a <= b 返回1，否则返回0
template<typename T1, typename T2>
auto LEthan(const T1& a, const T2& b) {
    auto result = xt::cast<double>(a <= b);
    return result;
}

// 等于比较：a == b 返回1，否则返回0
template<typename T1, typename T2>
auto Equal(const T1& a, const T2& b) {
    auto result = xt::cast<double>(a == b);
    return result;
}

// 不等于比较：a != b 返回1，否则返回0
template<typename T1, typename T2>
auto UnEqual(const T1& a, const T2& b) {
    auto result = xt::cast<double>(a != b);
    return result;
}

} // namespace feature_operators_xt

#endif // FEATURE_OPERATORS_XT_COMPARISON_OPS_HPP
