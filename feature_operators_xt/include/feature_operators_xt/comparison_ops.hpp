#ifndef FEATURE_OPERATORS_XT_COMPARISON_OPS_HPP
#define FEATURE_OPERATORS_XT_COMPARISON_OPS_HPP

#include "feature_operators_xt/types.hpp"
#include <xtensor/xarray.hpp>
#include <xtensor/xmath.hpp>
#include <xtensor/xview.hpp>
#include <xtensor/xadapt.hpp>

namespace feature_operators_xt {

// 大于比较 - 严格按照Python版本逻辑
template<typename T1, typename T2>
auto Mthan(const T1& s1, const T2& s2) {
    // Python逻辑：
    // newData = data1 > data2
    // return newData * 1
    auto data1 = s1;
    auto data2 = s2;
    auto newData = data1 > data2;
    return xt::cast<double>(newData);
}

// 大于等于比较 - 严格按照Python版本逻辑
template<typename T1, typename T2>
auto MEthan(const T1& s1, const T2& s2) {
    // Python逻辑：
    // newData = data1 >= data2
    // return newData * 1
    auto data1 = s1;
    auto data2 = s2;
    auto newData = data1 >= data2;
    return xt::cast<double>(newData);
}

// 小于比较 - 严格按照Python版本逻辑
template<typename T1, typename T2>
auto Lthan(const T1& s1, const T2& s2) {
    // Python逻辑：
    // newData = data1 < data2
    // return newData * 1
    auto data1 = s1;
    auto data2 = s2;
    auto newData = data1 < data2;
    return xt::cast<double>(newData);
}

// 小于等于比较 - 严格按照Python版本逻辑
template<typename T1, typename T2>
auto LEthan(const T1& s1, const T2& s2) {
    // Python逻辑：
    // newData = data1 <= data2
    // return newData * 1
    auto data1 = s1;
    auto data2 = s2;
    auto newData = data1 <= data2;
    return xt::cast<double>(newData);
}

// 等于比较 - 严格按照Python版本逻辑
template<typename T1, typename T2>
auto Equal(const T1& s1, const T2& s2) {
    // Python逻辑：
    // newData = data1 == data2
    // return newData * 1
    auto data1 = s1;
    auto data2 = s2;
    auto newData = data1 == data2;
    return xt::cast<double>(newData);
}

// 不等于比较 - 严格按照Python版本逻辑
template<typename T1, typename T2>
auto UnEqual(const T1& s1, const T2& s2) {
    // Python逻辑：
    // newData = data1 != data2
    // return newData * 1
    auto data1 = s1;
    auto data2 = s2;
    auto newData = data1 != data2;
    return xt::cast<double>(newData);
}

// Min函数 - 严格按照Python版本逻辑
template<typename T1, typename T2>
auto Min(const T1& data1, const T2& data2) {
    // Python逻辑：
    // flag = data1 > data2
    // 若data1为数值，则以data2为基础
    // if (type(data1) == int) | (type(data1) == float):
    //     newData = data2.copy()
    //     newData[~flag] = data1
    // 若data2为数值，则以data1为基础
    // elif (type(data2) == int) | (type(data2) == float):
    //     newData = data1.copy()
    //     newData[flag] = data2
    // else:
    //     newData = data1.copy()
    //     newData[flag] = data2[flag]
    // return newData

    auto flag = data1 > data2;
    auto newData = data1;
    newData = xt::where(flag, data2, newData);
    return newData;
}

// Max函数 - 严格按照Python版本逻辑
template<typename T1, typename T2>
auto Max(const T1& data1, const T2& data2) {
    // Python逻辑：
    // flag = data1 < data2
    // 若data1为数值，则以data2为基础
    // if (type(data1) == int) | (type(data1) == float):
    //     newData = data2.copy()
    //     newData[~flag] = data1
    // 若data2为数值，则以data1为基础
    // elif (type(data2) == int) | (type(data2) == float):
    //     newData = data1.copy()
    //     newData[flag] = data2
    // else:
    //     newData = data1.copy()
    //     newData[flag] = data2[flag]
    // return newData

    auto flag = data1 < data2;
    auto newData = data1;
    newData = xt::where(flag, data2, newData);
    return newData;
}

} // namespace feature_operators_xt

#endif // FEATURE_OPERATORS_XT_COMPARISON_OPS_HPP
