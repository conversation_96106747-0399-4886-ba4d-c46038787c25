#ifndef FEATURE_OPERATORS_XT_LOGICAL_OPS_HPP
#define FEATURE_OPERATORS_XT_LOGICAL_OPS_HPP

#include "feature_operators_xt/types.hpp"
#include <xtensor/xarray.hpp>
#include <xtensor/xmath.hpp>
#include <xtensor/xview.hpp>
#include <xtensor/xadapt.hpp>
#include <xtensor/xcast.hpp>
#include <limits>
#include <cmath>

namespace feature_operators_xt {

// IfThen函数 - 严格按照Python版本逻辑
template<typename T1, typename T2, typename T3>
auto IfThen(const T1& condition, const T2& fillData1, const T3& fillData2) {
    // Python逻辑：
    // newData = condition.copy()
    // flag = (data > 0)
    // newData[flag] = fillData1
    // flag = (data < 0)
    // newData[flag] = fillData2
    // newData[data.isna()] = np.nan
    // return newData
    auto newData = condition;
    auto flag_pos = condition > 0.0;
    auto flag_neg = condition < 0.0;
    auto nan_mask = xt::isnan(condition);

    newData = xt::where(flag_pos, fillData1, newData);
    newData = xt::where(flag_neg, fillData2, newData);
    newData = xt::where(nan_mask, std::numeric_limits<double>::quiet_NaN(), newData);

    return newData;
}

// And函数 - 严格按照Python版本逻辑
template<typename T1, typename T2>
auto And(const T1& s1, const T2& s2) {
    // Python逻辑：
    // data1 = s1.copy()
    // if (type(s2) == int) | (type(s2) == float):
    //     data2 = pd.DataFrame(s2, index=s1.index, columns=s1.columns)
    // else:
    //     data2 = s2.copy()
    // Filter = data1.isna() | data2.isna()
    // newData = pd.DataFrame(False,index = data1.index,columns =data1.columns)
    // newData[~Filter] = (data1[~Filter] != 0) & (data2[~Filter] != 0)
    // return newData * 1

    auto data1 = s1;
    auto data2 = s2;

    auto filter_mask = xt::isnan(data1) || xt::isnan(data2);
    auto result = xt::zeros_like(data1);

    auto valid_mask = !filter_mask;
    auto data1_nonzero = xt::not_equal(data1, 0.0);
    auto data2_nonzero = xt::not_equal(data2, 0.0);
    auto logical_and = data1_nonzero && data2_nonzero;

    result = xt::where(valid_mask && logical_and, 1.0, 0.0);

    return result;
}

// Or函数 - 严格按照Python版本逻辑
template<typename T1, typename T2>
auto Or(const T1& s1, const T2& s2) {
    // Python逻辑：
    // Filter = np.isnan(data1) | np.isnan(data2)
    // newData = pd.DataFrame(False,index = data1.index,columns =data1.columns)
    // newData[~Filter] = (data1[~Filter] != 0) | (data2[~Filter] != 0)
    // return newData

    auto data1 = s1;
    auto data2 = s2;

    auto filter_mask = xt::isnan(data1) || xt::isnan(data2);
    auto result = xt::zeros_like(data1);

    auto valid_mask = !filter_mask;
    auto logical_or = (data1 != 0.0) || (data2 != 0.0);

    result = xt::where(valid_mask && logical_or, 1.0, 0.0);

    return result;
}

// Not函数 - 严格按照Python版本逻辑
template<typename T>
auto Not(const T& s1) {
    // Python逻辑：
    // data = s1.copy()
    // Filter = data.isna()
    // newData = pd.DataFrame(False,index = data.index,columns =data.columns)
    // newData[~Filter] = (data[~Filter] == 0)
    // newData[Filter] = False
    // return newData * 1

    auto data = s1;
    auto filter_mask = xt::isnan(data);
    auto result = xt::zeros_like(data);

    auto valid_mask = !filter_mask;
    auto logical_not = (data == 0.0);

    result = xt::where(valid_mask && logical_not, 1.0, 0.0);

    return result;
}

// Xor函数 - 严格按照Python版本逻辑
template<typename T1, typename T2>
auto Xor(const T1& s1, const T2& s2) {
    // Python逻辑：
    // Filter = data1.isna() | data2.isna()
    // newData = pd.DataFrame(False,index = data1.index,columns =data1.columns)
    // newData[~Filter] = ((data1[~Filter] != 0) & ~(data2[~Filter] != 0)) | (
    //             ~(data1[~Filter] != 0) & (data2[~Filter] != 0))
    // return newData * 1

    auto data1 = s1;
    auto data2 = s2;

    auto filter_mask = xt::isnan(data1) || xt::isnan(data2);
    auto result = xt::zeros_like(data1);

    auto valid_mask = !filter_mask;
    auto data1_nonzero = (data1 != 0.0);
    auto data2_nonzero = (data2 != 0.0);
    auto logical_xor = (data1_nonzero && !data2_nonzero) || (!data1_nonzero && data2_nonzero);

    result = xt::where(valid_mask && logical_xor, 1.0, 0.0);

    return result;
}

} // namespace feature_operators_xt

#endif // FEATURE_OPERATORS_XT_LOGICAL_OPS_HPP
