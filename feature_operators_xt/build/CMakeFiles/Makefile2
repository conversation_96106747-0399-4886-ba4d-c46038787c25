# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.29

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/local/bin/cmake

# The command to remove a file.
RM = /usr/local/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/feature_operators_xt

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/feature_operators_xt/build

#=============================================================================
# Directory level rules for the build root directory

# The main recursive "all" target.
all: CMakeFiles/demo_basic.dir/all
all: CMakeFiles/test_operators.dir/all
all: CMakeFiles/unified_correctness_test.dir/all
all: CMakeFiles/simple_test.dir/all
all: CMakeFiles/basic_ops_test.dir/all
.PHONY : all

# The main recursive "preinstall" target.
preinstall:
.PHONY : preinstall

# The main recursive "clean" target.
clean: CMakeFiles/demo_basic.dir/clean
clean: CMakeFiles/test_operators.dir/clean
clean: CMakeFiles/unified_correctness_test.dir/clean
clean: CMakeFiles/simple_test.dir/clean
clean: CMakeFiles/basic_ops_test.dir/clean
.PHONY : clean

#=============================================================================
# Target rules for target CMakeFiles/demo_basic.dir

# All Build rule for target.
CMakeFiles/demo_basic.dir/all:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/demo_basic.dir/build.make CMakeFiles/demo_basic.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/demo_basic.dir/build.make CMakeFiles/demo_basic.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/home/<USER>/feature_operators_xt/build/CMakeFiles --progress-num=3,4 "Built target demo_basic"
.PHONY : CMakeFiles/demo_basic.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/demo_basic.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/feature_operators_xt/build/CMakeFiles 2
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/demo_basic.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/feature_operators_xt/build/CMakeFiles 0
.PHONY : CMakeFiles/demo_basic.dir/rule

# Convenience name for target.
demo_basic: CMakeFiles/demo_basic.dir/rule
.PHONY : demo_basic

# clean rule for target.
CMakeFiles/demo_basic.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/demo_basic.dir/build.make CMakeFiles/demo_basic.dir/clean
.PHONY : CMakeFiles/demo_basic.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/test_operators.dir

# All Build rule for target.
CMakeFiles/test_operators.dir/all:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_operators.dir/build.make CMakeFiles/test_operators.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_operators.dir/build.make CMakeFiles/test_operators.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/home/<USER>/feature_operators_xt/build/CMakeFiles --progress-num=7,8 "Built target test_operators"
.PHONY : CMakeFiles/test_operators.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/test_operators.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/feature_operators_xt/build/CMakeFiles 2
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/test_operators.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/feature_operators_xt/build/CMakeFiles 0
.PHONY : CMakeFiles/test_operators.dir/rule

# Convenience name for target.
test_operators: CMakeFiles/test_operators.dir/rule
.PHONY : test_operators

# clean rule for target.
CMakeFiles/test_operators.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_operators.dir/build.make CMakeFiles/test_operators.dir/clean
.PHONY : CMakeFiles/test_operators.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/unified_correctness_test.dir

# All Build rule for target.
CMakeFiles/unified_correctness_test.dir/all:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/unified_correctness_test.dir/build.make CMakeFiles/unified_correctness_test.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/unified_correctness_test.dir/build.make CMakeFiles/unified_correctness_test.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/home/<USER>/feature_operators_xt/build/CMakeFiles --progress-num=9,10 "Built target unified_correctness_test"
.PHONY : CMakeFiles/unified_correctness_test.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/unified_correctness_test.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/feature_operators_xt/build/CMakeFiles 2
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/unified_correctness_test.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/feature_operators_xt/build/CMakeFiles 0
.PHONY : CMakeFiles/unified_correctness_test.dir/rule

# Convenience name for target.
unified_correctness_test: CMakeFiles/unified_correctness_test.dir/rule
.PHONY : unified_correctness_test

# clean rule for target.
CMakeFiles/unified_correctness_test.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/unified_correctness_test.dir/build.make CMakeFiles/unified_correctness_test.dir/clean
.PHONY : CMakeFiles/unified_correctness_test.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/simple_test.dir

# All Build rule for target.
CMakeFiles/simple_test.dir/all:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/simple_test.dir/build.make CMakeFiles/simple_test.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/simple_test.dir/build.make CMakeFiles/simple_test.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/home/<USER>/feature_operators_xt/build/CMakeFiles --progress-num=5,6 "Built target simple_test"
.PHONY : CMakeFiles/simple_test.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/simple_test.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/feature_operators_xt/build/CMakeFiles 2
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/simple_test.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/feature_operators_xt/build/CMakeFiles 0
.PHONY : CMakeFiles/simple_test.dir/rule

# Convenience name for target.
simple_test: CMakeFiles/simple_test.dir/rule
.PHONY : simple_test

# clean rule for target.
CMakeFiles/simple_test.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/simple_test.dir/build.make CMakeFiles/simple_test.dir/clean
.PHONY : CMakeFiles/simple_test.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/basic_ops_test.dir

# All Build rule for target.
CMakeFiles/basic_ops_test.dir/all:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/basic_ops_test.dir/build.make CMakeFiles/basic_ops_test.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/basic_ops_test.dir/build.make CMakeFiles/basic_ops_test.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/home/<USER>/feature_operators_xt/build/CMakeFiles --progress-num=1,2 "Built target basic_ops_test"
.PHONY : CMakeFiles/basic_ops_test.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/basic_ops_test.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/feature_operators_xt/build/CMakeFiles 2
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/basic_ops_test.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/feature_operators_xt/build/CMakeFiles 0
.PHONY : CMakeFiles/basic_ops_test.dir/rule

# Convenience name for target.
basic_ops_test: CMakeFiles/basic_ops_test.dir/rule
.PHONY : basic_ops_test

# clean rule for target.
CMakeFiles/basic_ops_test.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/basic_ops_test.dir/build.make CMakeFiles/basic_ops_test.dir/clean
.PHONY : CMakeFiles/basic_ops_test.dir/clean

#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

