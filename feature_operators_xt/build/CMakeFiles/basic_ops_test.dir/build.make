# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.29

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/local/bin/cmake

# The command to remove a file.
RM = /usr/local/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/feature_operators_xt

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/feature_operators_xt/build

# Include any dependencies generated for this target.
include CMakeFiles/basic_ops_test.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/basic_ops_test.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/basic_ops_test.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/basic_ops_test.dir/flags.make

CMakeFiles/basic_ops_test.dir/tests/basic_ops_test.cpp.o: CMakeFiles/basic_ops_test.dir/flags.make
CMakeFiles/basic_ops_test.dir/tests/basic_ops_test.cpp.o: /home/<USER>/feature_operators_xt/tests/basic_ops_test.cpp
CMakeFiles/basic_ops_test.dir/tests/basic_ops_test.cpp.o: CMakeFiles/basic_ops_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/feature_operators_xt/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object CMakeFiles/basic_ops_test.dir/tests/basic_ops_test.cpp.o"
	/opt/rh/gcc-toolset-11/root/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/basic_ops_test.dir/tests/basic_ops_test.cpp.o -MF CMakeFiles/basic_ops_test.dir/tests/basic_ops_test.cpp.o.d -o CMakeFiles/basic_ops_test.dir/tests/basic_ops_test.cpp.o -c /home/<USER>/feature_operators_xt/tests/basic_ops_test.cpp

CMakeFiles/basic_ops_test.dir/tests/basic_ops_test.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/basic_ops_test.dir/tests/basic_ops_test.cpp.i"
	/opt/rh/gcc-toolset-11/root/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/feature_operators_xt/tests/basic_ops_test.cpp > CMakeFiles/basic_ops_test.dir/tests/basic_ops_test.cpp.i

CMakeFiles/basic_ops_test.dir/tests/basic_ops_test.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/basic_ops_test.dir/tests/basic_ops_test.cpp.s"
	/opt/rh/gcc-toolset-11/root/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/feature_operators_xt/tests/basic_ops_test.cpp -o CMakeFiles/basic_ops_test.dir/tests/basic_ops_test.cpp.s

# Object files for target basic_ops_test
basic_ops_test_OBJECTS = \
"CMakeFiles/basic_ops_test.dir/tests/basic_ops_test.cpp.o"

# External object files for target basic_ops_test
basic_ops_test_EXTERNAL_OBJECTS =

basic_ops_test: CMakeFiles/basic_ops_test.dir/tests/basic_ops_test.cpp.o
basic_ops_test: CMakeFiles/basic_ops_test.dir/build.make
basic_ops_test: CMakeFiles/basic_ops_test.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=/home/<USER>/feature_operators_xt/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Linking CXX executable basic_ops_test"
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/basic_ops_test.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/basic_ops_test.dir/build: basic_ops_test
.PHONY : CMakeFiles/basic_ops_test.dir/build

CMakeFiles/basic_ops_test.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/basic_ops_test.dir/cmake_clean.cmake
.PHONY : CMakeFiles/basic_ops_test.dir/clean

CMakeFiles/basic_ops_test.dir/depend:
	cd /home/<USER>/feature_operators_xt/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/feature_operators_xt /home/<USER>/feature_operators_xt /home/<USER>/feature_operators_xt/build /home/<USER>/feature_operators_xt/build /home/<USER>/feature_operators_xt/build/CMakeFiles/basic_ops_test.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : CMakeFiles/basic_ops_test.dir/depend

