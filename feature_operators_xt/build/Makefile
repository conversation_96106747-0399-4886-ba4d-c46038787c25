# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.29

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/local/bin/cmake

# The command to remove a file.
RM = /usr/local/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/feature_operators_xt

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/feature_operators_xt/build

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running CMake cache editor..."
	/usr/local/bin/ccmake -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache
.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running CMake to regenerate build system..."
	/usr/local/bin/cmake --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache
.PHONY : rebuild_cache/fast

# Special rule for the target list_install_components
list_install_components:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Available install components are: \"Unspecified\""
.PHONY : list_install_components

# Special rule for the target list_install_components
list_install_components/fast: list_install_components
.PHONY : list_install_components/fast

# Special rule for the target install
install: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Install the project..."
	/usr/local/bin/cmake -P cmake_install.cmake
.PHONY : install

# Special rule for the target install
install/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Install the project..."
	/usr/local/bin/cmake -P cmake_install.cmake
.PHONY : install/fast

# Special rule for the target install/local
install/local: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing only the local directory..."
	/usr/local/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local

# Special rule for the target install/local
install/local/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing only the local directory..."
	/usr/local/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local/fast

# Special rule for the target install/strip
install/strip: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing the project stripped..."
	/usr/local/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip

# Special rule for the target install/strip
install/strip/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing the project stripped..."
	/usr/local/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip/fast

# The main all target
all: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/feature_operators_xt/build/CMakeFiles /home/<USER>/feature_operators_xt/build//CMakeFiles/progress.marks
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/feature_operators_xt/build/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 clean
.PHONY : clean

# The main clean target
clean/fast: clean
.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

#=============================================================================
# Target rules for targets named demo_basic

# Build rule for target.
demo_basic: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 demo_basic
.PHONY : demo_basic

# fast build rule for target.
demo_basic/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/demo_basic.dir/build.make CMakeFiles/demo_basic.dir/build
.PHONY : demo_basic/fast

#=============================================================================
# Target rules for targets named test_operators

# Build rule for target.
test_operators: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 test_operators
.PHONY : test_operators

# fast build rule for target.
test_operators/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_operators.dir/build.make CMakeFiles/test_operators.dir/build
.PHONY : test_operators/fast

#=============================================================================
# Target rules for targets named unified_correctness_test

# Build rule for target.
unified_correctness_test: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 unified_correctness_test
.PHONY : unified_correctness_test

# fast build rule for target.
unified_correctness_test/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/unified_correctness_test.dir/build.make CMakeFiles/unified_correctness_test.dir/build
.PHONY : unified_correctness_test/fast

#=============================================================================
# Target rules for targets named simple_test

# Build rule for target.
simple_test: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 simple_test
.PHONY : simple_test

# fast build rule for target.
simple_test/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/simple_test.dir/build.make CMakeFiles/simple_test.dir/build
.PHONY : simple_test/fast

#=============================================================================
# Target rules for targets named basic_ops_test

# Build rule for target.
basic_ops_test: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 basic_ops_test
.PHONY : basic_ops_test

# fast build rule for target.
basic_ops_test/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/basic_ops_test.dir/build.make CMakeFiles/basic_ops_test.dir/build
.PHONY : basic_ops_test/fast

examples/demo_basic.o: examples/demo_basic.cpp.o
.PHONY : examples/demo_basic.o

# target to build an object file
examples/demo_basic.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/demo_basic.dir/build.make CMakeFiles/demo_basic.dir/examples/demo_basic.cpp.o
.PHONY : examples/demo_basic.cpp.o

examples/demo_basic.i: examples/demo_basic.cpp.i
.PHONY : examples/demo_basic.i

# target to preprocess a source file
examples/demo_basic.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/demo_basic.dir/build.make CMakeFiles/demo_basic.dir/examples/demo_basic.cpp.i
.PHONY : examples/demo_basic.cpp.i

examples/demo_basic.s: examples/demo_basic.cpp.s
.PHONY : examples/demo_basic.s

# target to generate assembly for a file
examples/demo_basic.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/demo_basic.dir/build.make CMakeFiles/demo_basic.dir/examples/demo_basic.cpp.s
.PHONY : examples/demo_basic.cpp.s

tests/basic_ops_test.o: tests/basic_ops_test.cpp.o
.PHONY : tests/basic_ops_test.o

# target to build an object file
tests/basic_ops_test.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/basic_ops_test.dir/build.make CMakeFiles/basic_ops_test.dir/tests/basic_ops_test.cpp.o
.PHONY : tests/basic_ops_test.cpp.o

tests/basic_ops_test.i: tests/basic_ops_test.cpp.i
.PHONY : tests/basic_ops_test.i

# target to preprocess a source file
tests/basic_ops_test.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/basic_ops_test.dir/build.make CMakeFiles/basic_ops_test.dir/tests/basic_ops_test.cpp.i
.PHONY : tests/basic_ops_test.cpp.i

tests/basic_ops_test.s: tests/basic_ops_test.cpp.s
.PHONY : tests/basic_ops_test.s

# target to generate assembly for a file
tests/basic_ops_test.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/basic_ops_test.dir/build.make CMakeFiles/basic_ops_test.dir/tests/basic_ops_test.cpp.s
.PHONY : tests/basic_ops_test.cpp.s

tests/simple_test.o: tests/simple_test.cpp.o
.PHONY : tests/simple_test.o

# target to build an object file
tests/simple_test.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/simple_test.dir/build.make CMakeFiles/simple_test.dir/tests/simple_test.cpp.o
.PHONY : tests/simple_test.cpp.o

tests/simple_test.i: tests/simple_test.cpp.i
.PHONY : tests/simple_test.i

# target to preprocess a source file
tests/simple_test.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/simple_test.dir/build.make CMakeFiles/simple_test.dir/tests/simple_test.cpp.i
.PHONY : tests/simple_test.cpp.i

tests/simple_test.s: tests/simple_test.cpp.s
.PHONY : tests/simple_test.s

# target to generate assembly for a file
tests/simple_test.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/simple_test.dir/build.make CMakeFiles/simple_test.dir/tests/simple_test.cpp.s
.PHONY : tests/simple_test.cpp.s

tests/test_operators.o: tests/test_operators.cpp.o
.PHONY : tests/test_operators.o

# target to build an object file
tests/test_operators.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_operators.dir/build.make CMakeFiles/test_operators.dir/tests/test_operators.cpp.o
.PHONY : tests/test_operators.cpp.o

tests/test_operators.i: tests/test_operators.cpp.i
.PHONY : tests/test_operators.i

# target to preprocess a source file
tests/test_operators.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_operators.dir/build.make CMakeFiles/test_operators.dir/tests/test_operators.cpp.i
.PHONY : tests/test_operators.cpp.i

tests/test_operators.s: tests/test_operators.cpp.s
.PHONY : tests/test_operators.s

# target to generate assembly for a file
tests/test_operators.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_operators.dir/build.make CMakeFiles/test_operators.dir/tests/test_operators.cpp.s
.PHONY : tests/test_operators.cpp.s

tests/unified_correctness_test.o: tests/unified_correctness_test.cpp.o
.PHONY : tests/unified_correctness_test.o

# target to build an object file
tests/unified_correctness_test.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/unified_correctness_test.dir/build.make CMakeFiles/unified_correctness_test.dir/tests/unified_correctness_test.cpp.o
.PHONY : tests/unified_correctness_test.cpp.o

tests/unified_correctness_test.i: tests/unified_correctness_test.cpp.i
.PHONY : tests/unified_correctness_test.i

# target to preprocess a source file
tests/unified_correctness_test.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/unified_correctness_test.dir/build.make CMakeFiles/unified_correctness_test.dir/tests/unified_correctness_test.cpp.i
.PHONY : tests/unified_correctness_test.cpp.i

tests/unified_correctness_test.s: tests/unified_correctness_test.cpp.s
.PHONY : tests/unified_correctness_test.s

# target to generate assembly for a file
tests/unified_correctness_test.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/unified_correctness_test.dir/build.make CMakeFiles/unified_correctness_test.dir/tests/unified_correctness_test.cpp.s
.PHONY : tests/unified_correctness_test.cpp.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... edit_cache"
	@echo "... install"
	@echo "... install/local"
	@echo "... install/strip"
	@echo "... list_install_components"
	@echo "... rebuild_cache"
	@echo "... basic_ops_test"
	@echo "... demo_basic"
	@echo "... simple_test"
	@echo "... test_operators"
	@echo "... unified_correctness_test"
	@echo "... examples/demo_basic.o"
	@echo "... examples/demo_basic.i"
	@echo "... examples/demo_basic.s"
	@echo "... tests/basic_ops_test.o"
	@echo "... tests/basic_ops_test.i"
	@echo "... tests/basic_ops_test.s"
	@echo "... tests/simple_test.o"
	@echo "... tests/simple_test.i"
	@echo "... tests/simple_test.s"
	@echo "... tests/test_operators.o"
	@echo "... tests/test_operators.i"
	@echo "... tests/test_operators.s"
	@echo "... tests/unified_correctness_test.o"
	@echo "... tests/unified_correctness_test.i"
	@echo "... tests/unified_correctness_test.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

