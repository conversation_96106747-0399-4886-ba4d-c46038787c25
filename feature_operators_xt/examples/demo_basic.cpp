#include <iostream>
#include <xtensor/xarray.hpp>
#include <xtensor/xio.hpp>
#include <xtensor/xrandom.hpp>
#include "feature_operators_xt.hpp"

using namespace feature_operators_xt;

int main() {
    std::cout << "=== Feature Operators XT 基础演示 ===" << std::endl;
    
    // 创建测试数据
    auto data = xt::random::randn<double>({10, 5});
    std::cout << "原始数据 (10x5):" << std::endl;
    std::cout << data << std::endl << std::endl;
    
    // 测试基础数学运算
    std::cout << "=== 基础数学运算 ===" << std::endl;
    
    auto data_plus_one = Add(data, 1.0);
    std::cout << "数据 + 1:" << std::endl;
    std::cout << data_plus_one << std::endl << std::endl;
    
    auto data_squared = Power(data, 2.0);
    std::cout << "数据的平方:" << std::endl;
    std::cout << data_squared << std::endl << std::endl;
    
    auto data_abs = Abs(data);
    std::cout << "数据的绝对值:" << std::endl;
    std::cout << data_abs << std::endl << std::endl;
    
    // 测试逻辑运算
    std::cout << "=== 逻辑运算 ===" << std::endl;
    
    auto positive_mask = Mthan(data, 0.0);
    std::cout << "大于0的掩码:" << std::endl;
    std::cout << positive_mask << std::endl << std::endl;
    
    auto if_then_result = IfThen(data, 1.0, -1.0);
    std::cout << "IfThen结果 (>0为1, <0为-1):" << std::endl;
    std::cout << if_then_result << std::endl << std::endl;
    
    // 测试时间序列运算
    std::cout << "=== 时间序列运算 ===" << std::endl;
    
    auto delayed_data = ts_Delay(data, 2);
    std::cout << "延迟2期的数据:" << std::endl;
    std::cout << delayed_data << std::endl << std::endl;
    
    auto rolling_mean = ts_Mean(data, 3);
    std::cout << "3期滚动均值:" << std::endl;
    std::cout << rolling_mean << std::endl << std::endl;
    
    auto delta_data = ts_Delta(data, 1);
    std::cout << "1期差分:" << std::endl;
    std::cout << delta_data << std::endl << std::endl;
    
    auto rolling_max = ts_Max(data, 3);
    std::cout << "3期滚动最大值:" << std::endl;
    std::cout << rolling_max << std::endl << std::endl;
    
    // 测试数据处理
    std::cout << "=== 数据处理 ===" << std::endl;
    
    // 创建包含NaN的数据
    auto data_with_nan = data;
    data_with_nan(2, 1) = std::numeric_limits<double>::quiet_NaN();
    data_with_nan(5, 3) = std::numeric_limits<double>::quiet_NaN();
    
    std::cout << "包含NaN的数据:" << std::endl;
    std::cout << data_with_nan << std::endl << std::endl;
    
    auto filled_data = FillNan(data_with_nan, 0.0);
    std::cout << "用0填充NaN后的数据:" << std::endl;
    std::cout << filled_data << std::endl << std::endl;
    
    auto min_data = Min(data, 0.5);
    std::cout << "与0.5取最小值:" << std::endl;
    std::cout << min_data << std::endl << std::endl;
    
    std::cout << "=== 演示完成 ===" << std::endl;
    
    return 0;
}
