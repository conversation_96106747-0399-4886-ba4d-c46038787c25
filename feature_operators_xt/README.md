# Feature Operators XT

基于xtensor的特征算子库，是原有Eigen3版本feature_operators的xtensor重新实现版本。

## 概述

Feature Operators XT提供了一套完整的金融特征工程算子，包括：

- **基础数学运算**: Add, Minus, Multiply, Divide, Sqrt, Log, Power, Exp等
- **逻辑运算**: And, Or, Not, Xor, IfThen等
- **比较运算**: <PERSON><PERSON>, <PERSON>than, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Equal, UnEqual等
- **时间序列运算**: ts_Delay, ts_Mean, ts_Stdev, ts_Delta, ts_Min, ts_Max等
- **数据处理工具**: FilterInf, <PERSON>ll<PERSON><PERSON>, Min, Max等

## 特性

- **高性能**: 基于xtensor，提供高效的张量运算
- **模板化设计**: 支持不同数据类型和维度
- **函数名一致**: 与原Python和Eigen3版本保持完全相同的函数名
- **命名空间隔离**: 使用`feature_operators_xt`命名空间避免冲突

## 依赖

- C++17或更高版本
- xtensor库
- CMake 3.12或更高版本

## 安装

### 使用CMake构建

```bash
mkdir build
cd build
cmake ..
make
```

### 运行示例

```bash
./demo_basic
```

### 运行测试

```bash
./test_operators
```

## 使用示例

```cpp
#include <xtensor/xarray.hpp>
#include "feature_operators_xt.hpp"

using namespace feature_operators_xt;

int main() {
    // 创建测试数据
    xt::xarray<double> data = {{1.0, 2.0, 3.0},
                               {4.0, 5.0, 6.0}};

    // 基础数学运算
    auto result1 = Add(data, 1.0);           // 每个元素加1
    auto result2 = Power(data, 2.0);         // 每个元素平方
    auto result3 = Sqrt(data);               // 每个元素开方

    // 逻辑运算
    auto mask = Mthan(data, 3.0);            // 大于3的掩码
    auto conditional = IfThen(data, 1.0, 0.0); // 条件赋值

    // 时间序列运算
    auto delayed = ts_Delay(data, 1);        // 延迟1期
    auto rolling_mean = ts_Mean(data, 3);    // 3期滚动均值
    auto delta = ts_Delta(data, 1);          // 1期差分

    return 0;
}
```

## API文档

### 基础数学运算

- `Add(s1, s2)` - 加法运算
- `Minus(s1, s2)` - 减法运算
- `Multiply(s1, s2)` - 乘法运算
- `Divide(s1, s2)` - 除法运算
- `Sqrt(s1)` - 平方根 (s1**0.5)
- `log(s1)` - 自然对数
- `inv(s1)` - 倒数 (1/s1)
- `Floor(data)` - 向下取整
- `Ceil(data)` - 向上取整
- `Round(data)` - 四舍五入
- `Abs(data)` - 绝对值
- `Log(data)` - 自然对数（处理非正数为NaN）
- `Sign(data)` - 符号函数
- `Reverse(data)` - 取负值 (data * -1)
- `Power(s1, n)` - 幂运算 (整数幂)
- `Exp(data)` - 指数运算
- `SignedPower(s1, Power)` - 带符号的幂运算

### 逻辑运算

- `IfThen(condition, fillData1, fillData2)` - 条件选择
- `And(s1, s2)` - 逻辑与
- `Or(s1, s2)` - 逻辑或
- `Not(s1)` - 逻辑非
- `Xor(s1, s2)` - 逻辑异或

### 比较运算

- `Mthan(s1, s2)` - 大于比较 (>)
- `MEthan(s1, s2)` - 大于等于比较 (>=)
- `Lthan(s1, s2)` - 小于比较 (<)
- `LEthan(s1, s2)` - 小于等于比较 (<=)
- `Equal(s1, s2)` - 等于比较 (==)
- `UnEqual(s1, s2)` - 不等于比较 (!=)
- `Min(data1, data2)` - 元素级最小值
- `Max(data1, data2)` - 元素级最大值

### 面板运算 (Panel Operations)

- `pn_Mean(s1)` - 面板均值（横截面平均值）
- `pn_Rank(s2)` - 面板排序（百分位排名）
- `pn_Rank2(s1)` - 面板排序（序号排名）
- `pn_RankCentered(s1)` - 面板中心化排名 ([-1, 1])
- `pn_FillMax(s1)` - 面板最大值填充
- `pn_FillMin(s1)` - 面板最小值填充
- `pn_Cut(s1)` - 面板截断（保留20%-80%分位数）
- `pn_Winsor(s1, Multiplier)` - 面板Winsor化
- `pn_TransStd(s1)` - 面板标准化
- `pn_CrossFit(s1, s2)` - 面板交叉拟合

### 时间序列运算 (Time Series Operations)

- `ts_Delay(s1, n)` - 时间序列延迟
- `ts_Mean(s1, n)` - 滚动均值
- `ts_Stdev(s1, n)` - 滚动标准差
- `ts_Delta(s1, n)` - 时间序列差分
- `ts_Min(data, n)` - 滚动最小值
- `ts_Max(data, n)` - 滚动最大值
- `ts_Sum(data, n)` - 滚动求和
- `ts_ChgRate(data, n)` - 变化率
- `ts_Divide(data, n)` - 时间序列除法

### 数据处理工具

- `FilterInf(s1)` - 过滤无穷值
- `FillNan(data, fillData_)` - 填充NaN值
- `getNan(s1)` - 获取NaN数组
- `getInf(s1)` - 获取Inf数组

## 与原版本的对应关系

本xtensor版本与原Python版本(`feature_operator_funcs.py`)和Eigen3版本保持完全相同的函数名和逻辑，可以作为直接替换使用。

## 许可证

本项目遵循与原feature_operators项目相同的许可证。

## 贡献

欢迎提交Issue和Pull Request来改进这个项目。
