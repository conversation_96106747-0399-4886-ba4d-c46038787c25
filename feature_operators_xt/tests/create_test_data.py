#!/usr/bin/env python3
"""
创建测试数据
"""

import os
import numpy as np
import pandas as pd

def create_test_data():
    """创建简单的测试数据"""
    
    # 设置随机种子以确保可重复性
    np.random.seed(42)
    
    # 创建数据维度
    n_rows = 100  # 时间序列长度
    n_cols = 10   # 股票数量
    
    # 创建日期索引
    dates = pd.date_range('2023-01-01', periods=n_rows, freq='D')
    
    # 创建股票代码
    stocks = [f'STOCK_{i:03d}' for i in range(n_cols)]
    
    # 生成基础价格数据
    base_price = 100.0
    price_data = []
    
    for i in range(n_rows):
        if i == 0:
            # 第一行数据
            row = base_price + np.random.normal(0, 5, n_cols)
        else:
            # 基于前一行生成数据，模拟价格随机游走
            prev_row = price_data[-1]
            row = prev_row * (1 + np.random.normal(0, 0.02, n_cols))
        
        price_data.append(row)
    
    price_data = np.array(price_data)
    
    # 确保价格为正数
    price_data = np.abs(price_data)
    
    # 生成开盘价、最高价、最低价、收盘价
    close = pd.DataFrame(price_data, index=dates, columns=stocks)
    
    # 开盘价：基于收盘价加上小幅随机变动
    open_data = price_data * (1 + np.random.normal(0, 0.01, (n_rows, n_cols)))
    open_price = pd.DataFrame(open_data, index=dates, columns=stocks)
    
    # 最高价：取开盘价和收盘价的最大值，再加上正向随机变动
    high_data = np.maximum(open_data, price_data) * (1 + np.abs(np.random.normal(0, 0.01, (n_rows, n_cols))))
    high = pd.DataFrame(high_data, index=dates, columns=stocks)
    
    # 最低价：取开盘价和收盘价的最小值，再减去正向随机变动
    low_data = np.minimum(open_data, price_data) * (1 - np.abs(np.random.normal(0, 0.01, (n_rows, n_cols))))
    low = pd.DataFrame(low_data, index=dates, columns=stocks)
    
    # 成交量：生成随机成交量数据
    volume_data = np.random.lognormal(10, 1, (n_rows, n_cols))
    volume = pd.DataFrame(volume_data, index=dates, columns=stocks)
    
    # 添加一些NaN值来测试NaN处理
    nan_indices = np.random.choice(n_rows * n_cols, size=int(0.05 * n_rows * n_cols), replace=False)
    for idx in nan_indices:
        row_idx = idx // n_cols
        col_idx = idx % n_cols
        close.iloc[row_idx, col_idx] = np.nan
        open_price.iloc[row_idx, col_idx] = np.nan
    
    return {
        'close': close,
        'open': open_price,
        'high': high,
        'low': low,
        'volume': volume
    }

def save_test_data(data, output_dir):
    """保存测试数据到CSV文件"""
    
    os.makedirs(output_dir, exist_ok=True)
    
    for name, df in data.items():
        filepath = os.path.join(output_dir, f"{name}.csv")
        df.to_csv(filepath)
        print(f"已保存 {name} 数据到 {filepath}")
        print(f"  形状: {df.shape}")
        print(f"  数据范围: {df.min().min():.2f} - {df.max().max():.2f}")
        print(f"  NaN数量: {df.isna().sum().sum()}")
        print()

if __name__ == "__main__":
    print("创建测试数据...")
    
    # 创建测试数据
    data = create_test_data()
    
    # 保存到测试数据目录
    output_dir = "./test_data"
    save_test_data(data, output_dir)
    
    print("测试数据创建完成!")
