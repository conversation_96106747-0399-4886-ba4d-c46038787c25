#include "../include/feature_operators_xt.hpp"
#include <iostream>
#include <iomanip>
#include <vector>
#include <string>
#include <cmath>
#include <fstream>
#include <sstream>
#include <map>
#include <xtensor/xarray.hpp>
#include <xtensor/xio.hpp>
#include <xtensor/xcsv.hpp>

using namespace feature_operators_xt;

// 从CSV文件加载数据
DataFrame loadCsv(const std::string& filepath) {
    std::ifstream file(filepath);
    if (!file.is_open()) {
        std::cerr << "无法打开文件: " << filepath << std::endl;
        return DataFrame();
    }

    std::vector<std::vector<double>> data;
    std::string line;

    // 跳过标题行
    std::getline(file, line);

    // 读取数据行
    while (std::getline(file, line)) {
        std::vector<double> row;
        std::stringstream ss(line);
        std::string cell;

        // 跳过第一列（日期）
        std::getline(ss, cell, ',');

        // 读取数据列
        while (std::getline(ss, cell, ',')) {
            try {
                double value = std::stod(cell);
                row.push_back(value);
            } catch (const std::exception& e) {
                row.push_back(std::numeric_limits<double>::quiet_NaN());
            }
        }

        if (!row.empty()) {
            data.push_back(row);
        }
    }

    // 创建xtensor数组
    int rows = data.size();
    int cols = rows > 0 ? data[0].size() : 0;

    DataFrame matrix = xt::zeros<double>({static_cast<std::size_t>(rows), static_cast<std::size_t>(cols)});
    for (int i = 0; i < rows; ++i) {
        for (int j = 0; j < cols; ++j) {
            matrix(i, j) = data[i][j];
        }
    }

    return matrix;
}

// 将DataFrame保存为CSV文件
void saveCsv(const DataFrame& data, const std::string& filepath) {
    // 创建目录
    std::string dir = filepath.substr(0, filepath.find_last_of('/'));
    std::string cmd = "mkdir -p " + dir;
    system(cmd.c_str());

    std::ofstream file(filepath);
    if (!file.is_open()) {
        std::cerr << "无法创建文件: " << filepath << std::endl;
        return;
    }

    // 使用科学计数法，保留10位小数
    file << std::scientific << std::setprecision(10);

    for (std::size_t i = 0; i < data.shape(0); ++i) {
        for (std::size_t j = 0; j < data.shape(1); ++j) {
            if (j > 0) file << ",";
            file << data(i, j);
        }
        file << "\n";
    }

    file.close();
    std::cout << "已保存到 " << filepath << std::endl;
}

// 运行核心数学算子测试
void testCoreMath(const DataFrame& close, const DataFrame& open, const DataFrame& volume,
                  const std::string& outputDir) {
    std::cout << "\n=== Core Math Operations Test ===" << std::endl;

    try {
        std::cout << "  测试 Add..." << std::endl;
        saveCsv(Add(close, open), outputDir + "/Add.csv");

        std::cout << "  测试 Minus..." << std::endl;
        saveCsv(Minus(close, open), outputDir + "/Minus.csv");

        std::cout << "  测试 Multiply..." << std::endl;
        saveCsv(Multiply(close, open), outputDir + "/Multiply.csv");

        std::cout << "  测试 Divide..." << std::endl;
        saveCsv(Divide(close, open), outputDir + "/Divide.csv");

        std::cout << "  测试 Sqrt..." << std::endl;
        saveCsv(Sqrt(close), outputDir + "/Sqrt.csv");

        std::cout << "  测试 Log..." << std::endl;
        saveCsv(Log(close), outputDir + "/Log.csv");

        std::cout << "  测试 inv..." << std::endl;
        saveCsv(inv(close), outputDir + "/inv.csv");

        std::cout << "  测试 Power..." << std::endl;
        saveCsv(Power(close, 2), outputDir + "/Power.csv");

        std::cout << "  测试 Abs..." << std::endl;
        saveCsv(Abs(Minus(close, open)), outputDir + "/Abs.csv");

        std::cout << "  测试 Sign..." << std::endl;
        saveCsv(Sign(Minus(close, open)), outputDir + "/Sign.csv");

        std::cout << "  测试 Exp..." << std::endl;
        saveCsv(Exp(Log(close)), outputDir + "/Exp.csv");

        std::cout << "  测试 Reverse..." << std::endl;
        saveCsv(Reverse(close), outputDir + "/Reverse.csv");

        std::cout << "  测试 Ceil..." << std::endl;
        saveCsv(Ceil(close), outputDir + "/Ceil.csv");

        std::cout << "  测试 Floor..." << std::endl;
        saveCsv(Floor(close), outputDir + "/Floor.csv");

        std::cout << "  测试 Round..." << std::endl;
        saveCsv(Round(close), outputDir + "/Round.csv");

        std::cout << "  测试 SignedPower..." << std::endl;
        saveCsv(SignedPower(close, 2.0), outputDir + "/SignedPower.csv");

    } catch (const std::exception& e) {
        std::cerr << "Core Math 测试错误: " << e.what() << std::endl;
    }
}

// 运行逻辑算子测试
void testLogicalOps(const DataFrame& close, const DataFrame& open, const std::string& outputDir) {
    std::cout << "\n=== Logical Operations Test ===" << std::endl;

    try {
        std::cout << "  测试 And..." << std::endl;
        saveCsv(And(close, open), outputDir + "/And.csv");

        std::cout << "  测试 Or..." << std::endl;
        saveCsv(Or(close, open), outputDir + "/Or.csv");

        std::cout << "  测试 Not..." << std::endl;
        saveCsv(Not(close), outputDir + "/Not.csv");

        std::cout << "  测试 Xor..." << std::endl;
        saveCsv(Xor(close, open), outputDir + "/Xor.csv");

    } catch (const std::exception& e) {
        std::cerr << "Logical Ops 测试错误: " << e.what() << std::endl;
    }
}

// 运行比较算子测试
void testComparisonOps(const DataFrame& close, const DataFrame& open, const std::string& outputDir) {
    std::cout << "\n=== Comparison Operations Test ===" << std::endl;

    try {
        std::cout << "  测试 Equal..." << std::endl;
        saveCsv(Equal(close, open), outputDir + "/Equal.csv");

        std::cout << "  测试 UnEqual..." << std::endl;
        saveCsv(UnEqual(close, open), outputDir + "/UnEqual.csv");

        std::cout << "  测试 Mthan..." << std::endl;
        saveCsv(Mthan(close, open), outputDir + "/Mthan.csv");

        std::cout << "  测试 MEthan..." << std::endl;
        saveCsv(MEthan(close, open), outputDir + "/MEthan.csv");

        std::cout << "  测试 Lthan..." << std::endl;
        saveCsv(Lthan(close, open), outputDir + "/Lthan.csv");

        std::cout << "  测试 LEthan..." << std::endl;
        saveCsv(LEthan(close, open), outputDir + "/LEthan.csv");

    } catch (const std::exception& e) {
        std::cerr << "Comparison Ops 测试错误: " << e.what() << std::endl;
    }
}

// 运行数据工具测试
void testDataUtils(const DataFrame& close, const std::string& outputDir) {
    std::cout << "\n=== Data Utils Test ===" << std::endl;

    try {
        std::cout << "  测试 FilterInf..." << std::endl;
        saveCsv(FilterInf(close), outputDir + "/FilterInf.csv");

        std::cout << "  测试 FillNan..." << std::endl;
        saveCsv(FillNan(close, 0.0), outputDir + "/FillNan.csv");

        std::cout << "  测试 getNan..." << std::endl;
        saveCsv(getNan(close), outputDir + "/getNan.csv");

        std::cout << "  测试 getInf..." << std::endl;
        saveCsv(getInf(close), outputDir + "/getInf.csv");

    } catch (const std::exception& e) {
        std::cerr << "Data Utils 测试错误: " << e.what() << std::endl;
    }
}

// 运行归约算子测试
void testReductionOps(const DataFrame& close, const DataFrame& open, const std::string& outputDir) {
    std::cout << "\n=== Reduction Operations Test ===" << std::endl;

    try {
        std::cout << "  测试 Min..." << std::endl;
        saveCsv(Min(close, open), outputDir + "/Min.csv");

        std::cout << "  测试 Max..." << std::endl;
        saveCsv(Max(close, open), outputDir + "/Max.csv");

    } catch (const std::exception& e) {
        std::cerr << "Reduction Ops 测试错误: " << e.what() << std::endl;
    }
}

// 运行时间序列算子测试
void testTimeseriesOps(const DataFrame& close, const DataFrame& open, const DataFrame& high,
                      const DataFrame& volume, const std::string& outputDir) {
    std::cout << "\n=== Time Series Operations Test ===" << std::endl;

    try {
        // 基础时间序列算子
        std::cout << "  测试 ts_Delay..." << std::endl;
        saveCsv(ts_Delay(close, 5), outputDir + "/ts_Delay.csv");

        std::cout << "  测试 ts_Mean..." << std::endl;
        saveCsv(ts_Mean(close, 10), outputDir + "/ts_Mean.csv");

        std::cout << "  测试 ts_Stdev..." << std::endl;
        saveCsv(ts_Stdev(close, 10), outputDir + "/ts_Stdev.csv");

        std::cout << "  测试 ts_Delta..." << std::endl;
        saveCsv(ts_Delta(close, 5), outputDir + "/ts_Delta.csv");

    } catch (const std::exception& e) {
        std::cerr << "Time Series Ops 测试错误: " << e.what() << std::endl;
    }
}

// 运行面板算子测试
void testPanelOps(const DataFrame& close, const DataFrame& open, const std::string& outputDir) {
    std::cout << "\n=== Panel Operations Test ===" << std::endl;

    try {
        std::cout << "  测试 pn_Mean..." << std::endl;
        saveCsv(pn_Mean(close), outputDir + "/pn_Mean.csv");

        std::cout << "  测试 pn_Rank..." << std::endl;
        saveCsv(pn_Rank(close), outputDir + "/pn_Rank.csv");

        std::cout << "  测试 pn_Rank2..." << std::endl;
        saveCsv(pn_Rank2(close), outputDir + "/pn_Rank2.csv");

        std::cout << "  测试 pn_RankCentered..." << std::endl;
        saveCsv(pn_RankCentered(close), outputDir + "/pn_RankCentered.csv");

        std::cout << "  测试 pn_FillMax..." << std::endl;
        saveCsv(pn_FillMax(close), outputDir + "/pn_FillMax.csv");

        std::cout << "  测试 pn_FillMin..." << std::endl;
        saveCsv(pn_FillMin(close), outputDir + "/pn_FillMin.csv");

        std::cout << "  测试 pn_TransStd..." << std::endl;
        saveCsv(pn_TransStd(close), outputDir + "/pn_TransStd.csv");

        std::cout << "  测试 pn_Winsor..." << std::endl;
        saveCsv(pn_Winsor(close, 3.0), outputDir + "/pn_Winsor.csv");

        std::cout << "  测试 pn_Cut..." << std::endl;
        saveCsv(pn_Cut(close), outputDir + "/pn_Cut.csv");

        std::cout << "  测试 pn_CrossFit..." << std::endl;
        saveCsv(pn_CrossFit(close, open), outputDir + "/pn_CrossFit.csv");

    } catch (const std::exception& e) {
        std::cerr << "Panel Ops 测试错误: " << e.what() << std::endl;
    }
}

int main(int argc, char* argv[]) {
    std::string category = "all";
    std::string dataDir = "./test_data";
    std::string outputBaseDir = "./results/cpp";

    // 解析命令行参数
    if (argc > 1) {
        category = argv[1];
    }

    std::cout << "Feature Operators XT Correctness Test Suite (C++)" << std::endl;
    std::cout << "===============================================" << std::endl;
    std::cout << "分类: " << category << std::endl;
    std::cout << "数据目录: " << dataDir << std::endl;

    // 加载数据
    DataFrame open = loadCsv(dataDir + "/open.csv");
    DataFrame high = loadCsv(dataDir + "/high.csv");
    DataFrame low = loadCsv(dataDir + "/low.csv");
    DataFrame close = loadCsv(dataDir + "/close.csv");
    DataFrame volume = loadCsv(dataDir + "/volume.csv");

    if (open.size() == 0 || high.size() == 0 || low.size() == 0 ||
        close.size() == 0 || volume.size() == 0) {
        std::cerr << "加载数据失败!" << std::endl;
        return 1;
    }

    std::cout << "数据加载成功: " << close.shape(0) << " 行, " << close.shape(1) << " 列" << std::endl;

    // 根据分类运行不同的测试
    if (category == "core_math" || category == "all") {
        testCoreMath(close, open, volume, outputBaseDir + "/core_math");
    }

    if (category == "logical_ops" || category == "all") {
        testLogicalOps(close, open, outputBaseDir + "/logical_ops");
    }

    if (category == "comparison_ops" || category == "all") {
        testComparisonOps(close, open, outputBaseDir + "/comparison_ops");
    }

    if (category == "data_utils" || category == "all") {
        testDataUtils(close, outputBaseDir + "/data_utils");
    }

    if (category == "reduction_ops" || category == "all") {
        testReductionOps(close, open, outputBaseDir + "/reduction_ops");
    }

    if (category == "timeseries_ops" || category == "all") {
        testTimeseriesOps(close, open, high, volume, outputBaseDir + "/timeseries_ops");
    }

    if (category == "panel_ops" || category == "all") {
        testPanelOps(close, open, outputBaseDir + "/panel_ops");
    }

    std::cout << "\nC++ XT 正确性测试完成!" << std::endl;
    return 0;
}
