#include <iostream>
#include <xtensor/xarray.hpp>
#include <xtensor/xio.hpp>
#include <xtensor/xmath.hpp>
#include <xtensor/xrandom.hpp>

// 简单测试，不依赖我们的头文件
int main() {
    std::cout << "=== XTensor 基础功能测试 ===" << std::endl;
    
    // 创建测试数据
    auto data1 = xt::random::randn<double>({5, 3});
    auto data2 = xt::random::randn<double>({5, 3});
    
    std::cout << "数据1:" << std::endl;
    std::cout << data1 << std::endl;
    
    std::cout << "\n数据2:" << std::endl;
    std::cout << data2 << std::endl;
    
    // 基础运算测试
    std::cout << "\n=== 基础运算测试 ===" << std::endl;
    
    auto add_result = data1 + data2;
    std::cout << "加法结果:" << std::endl;
    std::cout << add_result << std::endl;
    
    auto multiply_result = data1 * data2;
    std::cout << "\n乘法结果:" << std::endl;
    std::cout << multiply_result << std::endl;
    
    auto sqrt_result = xt::sqrt(xt::abs(data1));
    std::cout << "\n平方根结果:" << std::endl;
    std::cout << sqrt_result << std::endl;
    
    // 比较运算测试
    std::cout << "\n=== 比较运算测试 ===" << std::endl;
    
    auto greater_result = data1 > data2;
    std::cout << "大于比较结果:" << std::endl;
    std::cout << greater_result << std::endl;
    
    auto cast_result = xt::cast<double>(greater_result);
    std::cout << "\n转换为double结果:" << std::endl;
    std::cout << cast_result << std::endl;
    
    std::cout << "\n=== XTensor 基础功能测试完成 ===" << std::endl;
    
    return 0;
}
