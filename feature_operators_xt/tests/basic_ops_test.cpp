#include <iostream>
#include <fstream>
#include <iomanip>
#include <limits>
#include <xtensor/xarray.hpp>
#include <xtensor/xio.hpp>
#include <xtensor/xmath.hpp>
#include <xtensor/xrandom.hpp>


// 直接实现一些基础算子，不依赖我们的头文件
namespace test_ops {

// 基础数学运算
template<typename T1, typename T2>
auto Add(const T1& s1, const T2& s2) {
    return s1 + s2;
}

template<typename T1, typename T2>
auto Minus(const T1& s1, const T2& s2) {
    return s1 - s2;
}

template<typename T1, typename T2>
auto Multiply(const T1& s1, const T2& s2) {
    return s1 * s2;
}

template<typename T1, typename T2>
auto Divide(const T1& s1, const T2& s2) {
    return s1 / s2;
}

template<typename T>
auto Sqrt(const T& s1) {
    return xt::pow(s1, 0.5);
}

template<typename T>
auto Log(const T& data) {
    return xt::log(data);
}

template<typename T>
auto Abs(const T& data) {
    return xt::abs(data);
}

} // namespace test_ops

// 保存CSV文件的简单函数
void saveCsv(const xt::xarray<double>& data, const std::string& filepath) {
    std::string cmd = "mkdir -p " + filepath.substr(0, filepath.find_last_of('/'));
    system(cmd.c_str());

    std::ofstream file(filepath);
    if (!file.is_open()) {
        std::cerr << "无法创建文件: " << filepath << std::endl;
        return;
    }

    file << std::scientific << std::setprecision(10);

    for (std::size_t i = 0; i < data.shape(0); ++i) {
        for (std::size_t j = 0; j < data.shape(1); ++j) {
            if (j > 0) file << ",";
            file << data(i, j);
        }
        file << "\n";
    }

    file.close();
    std::cout << "已保存到 " << filepath << std::endl;
}

int main() {
    std::cout << "=== Feature Operators XT 基础算子测试 ===" << std::endl;

    // 创建测试数据
    xt::random::seed(42);
    auto close = xt::random::randn<double>({10, 5}) * 10.0 + 100.0;  // 模拟股价数据
    auto open = close + xt::random::randn<double>({10, 5}) * 0.5;    // 开盘价

    std::cout << "测试数据创建完成: " << close.shape(0) << " 行, " << close.shape(1) << " 列" << std::endl;

    std::cout << "\n收盘价数据:" << std::endl;
    std::cout << close << std::endl;

    std::cout << "\n开盘价数据:" << std::endl;
    std::cout << open << std::endl;

    // 测试基础数学运算
    std::cout << "\n=== 基础数学运算测试 ===" << std::endl;

    try {
        std::cout << "  测试 Add..." << std::endl;
        auto add_result = test_ops::Add(close, open);
        std::cout << "Add 结果形状: " << add_result.shape(0) << "x" << add_result.shape(1) << std::endl;
        saveCsv(add_result, "./results/Add.csv");

        std::cout << "  测试 Minus..." << std::endl;
        auto minus_result = test_ops::Minus(close, open);
        saveCsv(minus_result, "./results/Minus.csv");

        std::cout << "  测试 Multiply..." << std::endl;
        auto multiply_result = test_ops::Multiply(close, open);
        saveCsv(multiply_result, "./results/Multiply.csv");

        std::cout << "  测试 Divide..." << std::endl;
        auto divide_result = test_ops::Divide(close, open);
        saveCsv(divide_result, "./results/Divide.csv");

        std::cout << "  测试 Sqrt..." << std::endl;
        auto sqrt_result = test_ops::Sqrt(xt::abs(close));  // 确保非负
        saveCsv(sqrt_result, "./results/Sqrt.csv");

        std::cout << "  测试 Log..." << std::endl;
        auto log_result = test_ops::Log(xt::abs(close) + 1.0);  // 确保正数
        saveCsv(log_result, "./results/Log.csv");

        std::cout << "  测试 Abs..." << std::endl;
        auto abs_result = test_ops::Abs(test_ops::Minus(close, open));
        saveCsv(abs_result, "./results/Abs.csv");

    } catch (const std::exception& e) {
        std::cerr << "基础数学运算测试错误: " << e.what() << std::endl;
    }

    // 测试简单的比较运算
    std::cout << "\n=== 简单比较测试 ===" << std::endl;

    try {
        std::cout << "  测试基础比较..." << std::endl;
        auto comparison = close > open;
        std::cout << "Close > Open 结果:" << std::endl;
        std::cout << comparison << std::endl;

    } catch (const std::exception& e) {
        std::cerr << "比较运算测试错误: " << e.what() << std::endl;
    }

    std::cout << "\n=== 基础算子测试完成 ===" << std::endl;

    return 0;
}
