#include <iostream>
#include <cassert>
#include <cmath>
#include <xtensor/xarray.hpp>
#include <xtensor/xio.hpp>
#include "feature_operators_xt.hpp"

using namespace feature_operators_xt;

// 简单的测试框架
#define ASSERT_NEAR(a, b, tol) \
    do { \
        if (std::abs((a) - (b)) > (tol)) { \
            std::cerr << "断言失败: " << (a) << " != " << (b) << " (tolerance: " << (tol) << ")" << std::endl; \
            std::cerr << "在文件 " << __FILE__ << " 第 " << __LINE__ << " 行" << std::endl; \
            return false; \
        } \
    } while(0)

#define ASSERT_TRUE(condition) \
    do { \
        if (!(condition)) { \
            std::cerr << "断言失败: " << #condition << std::endl; \
            std::cerr << "在文件 " << __FILE__ << " 第 " << __LINE__ << " 行" << std::endl; \
            return false; \
        } \
    } while(0)

bool test_basic_math() {
    std::cout << "测试基础数学运算..." << std::endl;
    
    xt::xarray<double> a = {{1.0, 2.0}, {3.0, 4.0}};
    xt::xarray<double> b = {{2.0, 3.0}, {4.0, 5.0}};
    
    // 测试加法
    auto result_add = Add(a, b);
    ASSERT_NEAR(result_add(0, 0), 3.0, 1e-10);
    ASSERT_NEAR(result_add(1, 1), 9.0, 1e-10);
    
    // 测试减法
    auto result_minus = Minus(a, b);
    ASSERT_NEAR(result_minus(0, 0), -1.0, 1e-10);
    ASSERT_NEAR(result_minus(1, 1), -1.0, 1e-10);
    
    // 测试乘法
    auto result_multiply = Multiply(a, b);
    ASSERT_NEAR(result_multiply(0, 0), 2.0, 1e-10);
    ASSERT_NEAR(result_multiply(1, 1), 20.0, 1e-10);
    
    // 测试除法
    auto result_divide = Divide(a, b);
    ASSERT_NEAR(result_divide(0, 0), 0.5, 1e-10);
    ASSERT_NEAR(result_divide(1, 1), 0.8, 1e-10);
    
    // 测试平方根
    auto result_sqrt = Sqrt(a);
    ASSERT_NEAR(result_sqrt(0, 0), 1.0, 1e-10);
    ASSERT_NEAR(result_sqrt(1, 1), 2.0, 1e-10);
    
    // 测试幂运算
    auto result_power = Power(a, 2.0);
    ASSERT_NEAR(result_power(0, 0), 1.0, 1e-10);
    ASSERT_NEAR(result_power(1, 1), 16.0, 1e-10);
    
    std::cout << "基础数学运算测试通过!" << std::endl;
    return true;
}

bool test_logical_ops() {
    std::cout << "测试逻辑运算..." << std::endl;
    
    xt::xarray<double> a = {{1.0, 0.0}, {-1.0, 2.0}};
    xt::xarray<double> b = {{0.0, 1.0}, {-1.0, 0.0}};
    
    // 测试And
    auto result_and = And(a, b);
    ASSERT_NEAR(result_and(0, 0), 0.0, 1e-10);  // 1 && 0 = 0
    ASSERT_NEAR(result_and(0, 1), 0.0, 1e-10);  // 0 && 1 = 0
    ASSERT_NEAR(result_and(1, 0), 1.0, 1e-10);  // -1 && -1 = 1
    ASSERT_NEAR(result_and(1, 1), 0.0, 1e-10);  // 2 && 0 = 0
    
    // 测试Or
    auto result_or = Or(a, b);
    ASSERT_NEAR(result_or(0, 0), 1.0, 1e-10);   // 1 || 0 = 1
    ASSERT_NEAR(result_or(0, 1), 1.0, 1e-10);   // 0 || 1 = 1
    ASSERT_NEAR(result_or(1, 0), 1.0, 1e-10);   // -1 || -1 = 1
    ASSERT_NEAR(result_or(1, 1), 1.0, 1e-10);   // 2 || 0 = 1
    
    // 测试Not
    auto result_not = Not(a);
    ASSERT_NEAR(result_not(0, 0), 0.0, 1e-10);  // !1 = 0
    ASSERT_NEAR(result_not(0, 1), 1.0, 1e-10);  // !0 = 1
    ASSERT_NEAR(result_not(1, 0), 0.0, 1e-10);  // !(-1) = 0
    ASSERT_NEAR(result_not(1, 1), 0.0, 1e-10);  // !2 = 0
    
    std::cout << "逻辑运算测试通过!" << std::endl;
    return true;
}

bool test_comparison_ops() {
    std::cout << "测试比较运算..." << std::endl;
    
    xt::xarray<double> a = {{1.0, 2.0}, {3.0, 4.0}};
    xt::xarray<double> b = {{2.0, 2.0}, {1.0, 5.0}};
    
    // 测试大于
    auto result_mthan = Mthan(a, b);
    ASSERT_NEAR(result_mthan(0, 0), 0.0, 1e-10);  // 1 > 2 = false
    ASSERT_NEAR(result_mthan(0, 1), 0.0, 1e-10);  // 2 > 2 = false
    ASSERT_NEAR(result_mthan(1, 0), 1.0, 1e-10);  // 3 > 1 = true
    ASSERT_NEAR(result_mthan(1, 1), 0.0, 1e-10);  // 4 > 5 = false
    
    // 测试等于
    auto result_equal = Equal(a, b);
    ASSERT_NEAR(result_equal(0, 1), 1.0, 1e-10);  // 2 == 2 = true
    ASSERT_NEAR(result_equal(0, 0), 0.0, 1e-10);  // 1 == 2 = false
    
    std::cout << "比较运算测试通过!" << std::endl;
    return true;
}

bool test_timeseries_ops() {
    std::cout << "测试时间序列运算..." << std::endl;
    
    xt::xarray<double> data = {{1.0, 2.0}, {3.0, 4.0}, {5.0, 6.0}, {7.0, 8.0}};
    
    // 测试延迟
    auto delayed = ts_Delay(data, 1);
    ASSERT_TRUE(std::isnan(delayed(0, 0)));  // 第一行应该是NaN
    ASSERT_NEAR(delayed(1, 0), 1.0, 1e-10);  // 第二行应该是第一行的值
    ASSERT_NEAR(delayed(2, 1), 4.0, 1e-10);
    
    // 测试差分
    auto delta = ts_Delta(data, 1);
    ASSERT_TRUE(std::isnan(delta(0, 0)));    // 第一行应该是NaN
    ASSERT_NEAR(delta(1, 0), 2.0, 1e-10);   // 3 - 1 = 2
    ASSERT_NEAR(delta(2, 1), 2.0, 1e-10);   // 6 - 4 = 2
    
    // 测试滚动均值
    auto rolling_mean = ts_Mean(data, 2);
    ASSERT_NEAR(rolling_mean(0, 0), 1.0, 1e-10);    // 第一行只有自己
    ASSERT_NEAR(rolling_mean(1, 0), 2.0, 1e-10);    // (1+3)/2 = 2
    ASSERT_NEAR(rolling_mean(2, 1), 5.0, 1e-10);    // (4+6)/2 = 5
    
    std::cout << "时间序列运算测试通过!" << std::endl;
    return true;
}

bool test_data_utils() {
    std::cout << "测试数据处理工具..." << std::endl;
    
    xt::xarray<double> data = {{1.0, 2.0}, {3.0, 4.0}};
    
    // 测试Min和Max
    auto min_result = Min(data, 2.5);
    ASSERT_NEAR(min_result(0, 0), 1.0, 1e-10);  // min(1, 2.5) = 1
    ASSERT_NEAR(min_result(1, 1), 2.5, 1e-10);  // min(4, 2.5) = 2.5
    
    auto max_result = Max(data, 2.5);
    ASSERT_NEAR(max_result(0, 0), 2.5, 1e-10);  // max(1, 2.5) = 2.5
    ASSERT_NEAR(max_result(1, 1), 4.0, 1e-10);  // max(4, 2.5) = 4
    
    // 测试FillNan
    xt::xarray<double> data_with_nan = {{1.0, std::numeric_limits<double>::quiet_NaN()}, 
                                        {3.0, 4.0}};
    auto filled = FillNan(data_with_nan, -999.0);
    ASSERT_NEAR(filled(0, 0), 1.0, 1e-10);
    ASSERT_NEAR(filled(0, 1), -999.0, 1e-10);
    ASSERT_NEAR(filled(1, 0), 3.0, 1e-10);
    
    std::cout << "数据处理工具测试通过!" << std::endl;
    return true;
}

int main() {
    std::cout << "=== Feature Operators XT 测试套件 ===" << std::endl;
    
    bool all_passed = true;
    
    all_passed &= test_basic_math();
    all_passed &= test_logical_ops();
    all_passed &= test_comparison_ops();
    all_passed &= test_timeseries_ops();
    all_passed &= test_data_utils();
    
    if (all_passed) {
        std::cout << std::endl << "=== 所有测试通过! ===" << std::endl;
        return 0;
    } else {
        std::cout << std::endl << "=== 有测试失败! ===" << std::endl;
        return 1;
    }
}
