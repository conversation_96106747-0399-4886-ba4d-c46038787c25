cmake_minimum_required(VERSION 3.12)
project(feature_operators_xt)

# 设置C++标准
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# 查找xtensor
find_package(xtensor REQUIRED)

# 添加头文件目录
include_directories(include)

# 创建头文件库
add_library(feature_operators_xt INTERFACE)

# 设置头文件目录
target_include_directories(feature_operators_xt INTERFACE
    $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/include>
    $<INSTALL_INTERFACE:include>
)

# 链接xtensor
target_link_libraries(feature_operators_xt INTERFACE xtensor)

# 添加示例程序
add_executable(demo_basic examples/demo_basic.cpp)
target_link_libraries(demo_basic feature_operators_xt)

# 添加测试程序
add_executable(test_operators tests/test_operators.cpp)
target_link_libraries(test_operators feature_operators_xt)

# 安装配置
install(TARGETS feature_operators_xt
    EXPORT feature_operators_xt-targets
    LIBRARY DESTINATION lib
    ARCHIVE DESTINATION lib
    RUNTIME DESTINATION bin
    INCLUDES DESTINATION include
)

install(DIRECTORY include/ DESTINATION include)

install(EXPORT feature_operators_xt-targets
    FILE feature_operators_xt-targets.cmake
    NAMESPACE feature_operators_xt::
    DESTINATION lib/cmake/feature_operators_xt
)
