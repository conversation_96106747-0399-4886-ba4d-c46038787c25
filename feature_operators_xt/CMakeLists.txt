cmake_minimum_required(VERSION 3.12)
project(feature_operators_xt)

# 设置C++标准
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# 添加头文件目录
include_directories(include)

# 尝试查找xtensor，如果找不到就跳过
find_path(XTENSOR_INCLUDE_DIR xtensor/xarray.hpp
    PATHS /usr/include /usr/local/include /opt/include
    PATH_SUFFIXES xtensor
)

if(XTENSOR_INCLUDE_DIR)
    message(STATUS "Found xtensor at: ${XTENSOR_INCLUDE_DIR}")
    include_directories(${XTENSOR_INCLUDE_DIR})
else()
    message(WARNING "xtensor not found, some features may not work")
endif()

# 创建头文件库
add_library(feature_operators_xt INTERFACE)

# 设置头文件目录
target_include_directories(feature_operators_xt INTERFACE
    $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/include>
    $<INSTALL_INTERFACE:include>
)

# 添加示例程序
add_executable(demo_basic examples/demo_basic.cpp)
target_link_libraries(demo_basic feature_operators_xt)

# 添加测试程序
add_executable(test_operators tests/test_operators.cpp)
target_link_libraries(test_operators feature_operators_xt)

# 添加正确性测试程序
add_executable(unified_correctness_test tests/unified_correctness_test.cpp)
target_link_libraries(unified_correctness_test feature_operators_xt)

# 添加简单测试程序
add_executable(simple_test tests/simple_test.cpp)

# 添加基础算子测试程序
add_executable(basic_ops_test tests/basic_ops_test.cpp)

# 安装配置
install(TARGETS feature_operators_xt
    EXPORT feature_operators_xt-targets
    LIBRARY DESTINATION lib
    ARCHIVE DESTINATION lib
    RUNTIME DESTINATION bin
    INCLUDES DESTINATION include
)

install(DIRECTORY include/ DESTINATION include)

install(EXPORT feature_operators_xt-targets
    FILE feature_operators_xt-targets.cmake
    NAMESPACE feature_operators_xt::
    DESTINATION lib/cmake/feature_operators_xt
)
